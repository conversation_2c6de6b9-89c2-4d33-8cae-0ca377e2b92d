/* 相关标签页按钮样式 */
#related-tabs-button {
    position: relative;
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    margin-left: 4px;
}

#related-tabs-button:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

#related-tabs-button.related-tabs-on {
    background: var(--accent-color);
    color: white;
}

#related-tabs-button.related-tabs-on:hover {
    background: var(--accent-hover);
}

#related-tabs-button.loading {
    pointer-events: none;
}

#related-tabs-button.loading svg {
    animation: spin 1s linear infinite;
}

/* 标签页数量指示器 */
.tab-count {
    position: absolute;
    top: -4px;
    right: -4px;
    background: var(--accent-color);
    color: white;
    font-size: 10px;
    font-weight: bold;
    padding: 2px 4px;
    border-radius: 8px;
    min-width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
}

/* 相关标签页模态框 */
.related-tabs-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(4px);
}

.related-tabs-content {
    background: var(--bg-primary);
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    border: 1px solid var(--border-color);
}

.related-tabs-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.related-tabs-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.related-tabs-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.related-tabs-close:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.related-tabs-close svg {
    width: 20px;
    height: 20px;
}

/* 相关标签页状态区域 */
.related-tabs-status {
    padding: 20px;
    min-height: 200px;
    max-height: 400px;
    overflow-y: auto;
}

#related-tabs-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 12px;
    color: var(--text-secondary);
    min-height: 160px;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 相关标签页列表 */
.related-tabs-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.related-tab-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.related-tab-item:hover {
    background: var(--bg-hover);
    border-color: var(--accent-color);
}

.related-tab-item.selected {
    background: var(--accent-bg);
    border-color: var(--accent-color);
}

.related-tab-checkbox {
    margin-top: 2px;
    accent-color: var(--accent-color);
}

.related-tab-info {
    flex: 1;
    min-width: 0;
}

.related-tab-title {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.related-tab-url {
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.related-tab-reason {
    font-size: 12px;
    color: var(--text-tertiary);
    font-style: italic;
}

.related-tab-score {
    font-size: 11px;
    color: var(--accent-color);
    font-weight: 600;
    margin-top: 4px;
}

/* 空状态 */
#related-tabs-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 160px;
    color: var(--text-secondary);
    text-align: center;
}

/* 操作按钮区域 */
.related-tabs-actions {
    display: flex;
    gap: 8px;
    padding: 16px 20px;
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.btn-primary, .btn-secondary {
    flex: 1;
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background: var(--accent-color);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: var(--accent-hover);
}

.btn-primary:disabled {
    background: var(--bg-disabled);
    color: var(--text-disabled);
    cursor: not-allowed;
}

.btn-secondary {
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-hover);
}

/* 动画 */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 480px) {
    .related-tabs-content {
        width: 95%;
        margin: 10px;
    }
    
    .related-tabs-header {
        padding: 12px 16px;
    }
    
    .related-tabs-status {
        padding: 16px;
    }
    
    .related-tabs-actions {
        padding: 12px 16px;
        flex-direction: column;
    }
    
    .btn-primary, .btn-secondary {
        flex: none;
    }
}
