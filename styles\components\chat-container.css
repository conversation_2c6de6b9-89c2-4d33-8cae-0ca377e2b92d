/* 聊天容器样式 */
#chat-container {
    flex: 1;
    overflow-y: scroll;
    padding: 15px;
    padding-top: calc(15px + env(safe-area-inset-top));
    padding-bottom: calc(60px + env(safe-area-inset-bottom));
    scrollbar-width: none;
    -ms-overflow-style: none;
    min-height: 100%;
    -webkit-overflow-scrolling: touch;
    
    scroll-behavior: smooth;
    overscroll-behavior-y: contain;
    position: relative;
    height: 100%;
    box-sizing: border-box;
    margin-top: var(--chat-top-margin, 0px);
    transition: margin-top 0.3s ease;
}

#chat-container::-webkit-scrollbar {
    display: none;
}

.keyboard-visible #chat-container {
    padding-bottom: calc(60px + env(safe-area-inset-bottom) + var(--keyboard-height, 0px));
}