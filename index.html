<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1, user-scalable=no, viewport-fit=cover">
    <title>Cerebr</title>
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="application-name" content="Cerebr">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Cerebr">
    <link rel="apple-touch-icon" href="icons/icon128.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icons/icon16.png">
    <link rel="icon" type="image/png" sizes="48x48" href="icons/icon48.png">
    <link rel="icon" type="image/png" sizes="128x128" href="icons/icon128.png">
    <script src="htmd/marked.min.js"></script>
    <script src="htmd/highlight.min.js"></script>
    <script src="htmd/mermaid.min.js"></script>
    <script src="htmd/mermaid-init.js"></script>
    <script src="htmd/mathjax-config.js"></script>
    <script type="text/javascript" id="MathJax-script" async src="htmd/tex-chtml-full.js"></script>
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <div id="chat-container"></div>
    <div id="context-menu">
        <div class="context-menu-item" id="copy-message">
            <svg viewBox="0 0 24 24">
                <rect x="8" y="8" width="12" height="12" rx="1"/>
                <path d="M4 16V4a1 1 0 0 1 1-1h11"/>
            </svg>
            复制消息
        </div>
<div class="context-menu-item" id="edit-message">
            <svg viewBox="0 0 24 24">
                <path d="M12 20h9" />
                <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z" />
            </svg>
            编辑消息
        </div>
        <div class="context-menu-item" id="copy-code">
            <svg viewBox="0 0 24 24">
                <path d="M16 18L22 12L16 6"/>
                <path d="M8 6L2 12L8 18"/>
            </svg>
            复制代码
        </div>
        <div class="context-menu-item" id="copy-math">
            <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M6 19.5c.2 0 .32-.08.45-.25l5.75-8.5 5.65-8.2c.08-.1.1-.2.1-.28 0-.23-.2-.38-.43-.38-.2 0-.33.06-.48.28l-5.65 8.2-5.75 8.5c-.08.1-.1.2-.1.29 0 .24.2.34.46.34zM5.2 8.1v2.1c0 .27.17.42.41.42.23 0 .41-.15.41-.42V8.1h2c.27 0 .41-.17.41-.41 0-.23-.14-.41-.41-.41h-2V4.6c0-.27-.18-.42-.41-.42-.24 0-.41.15-.41.42v2.68h-2c-.27 0-.41.18-.41.41 0 .24.14.41.41.41h2zm9.2 8.6c0 .23.15.4.35.4h5.1c.27 0 .41-.17.41-.4 0-.23-.14-.4-.41-.4h-5.1c-.2 0-.35.17-.35.4z"/>
            </svg>
            复制公式
        </div>
        <div class="context-menu-item" id="stop-update">
            <svg viewBox="0 0 24 24">
                <path d="M6 6h12v12H6z"/>
            </svg>
            停止更新
        </div>
        <div class="context-menu-item" id="delete-message">
            <svg viewBox="0 0 24 24">
                <path d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7"/>
                <path d="M3 7h18"/>
                <path d="M9 7V4a1 1 0 011-1h4a1 1 0 011 1v3"/>
            </svg>
            删除消息
        </div>
    </div>
    <div id="chat-card-context-menu">
        <div class="context-menu-item" id="copy-chat-title">
            <svg viewBox="0 0 24 24">
                <rect x="8" y="8" width="12" height="12" rx="1"/>
                <path d="M4 16V4a1 1 0 0 1 1-1h11"/>
            </svg>
            复制标题
        </div>
        <div class="context-menu-item" id="copy-chat-link">
            <svg viewBox="0 0 24 24">
                <path d="M10 13a5 5 0 0 0 7.07 0l1.83-1.83a5 5 0 0 0-7.07-7.07L10 5"/>
                <path d="M14 11a5 5 0 0 0-7.07 0L5.1 12.83a5 5 0 0 0 7.07 7.07L14 18"/>
            </svg>
            复制链接
        </div>
        <div class="context-menu-item" id="open-chat-link">
            <svg viewBox="0 0 24 24">
                <path d="M14 3h7v7"/>
                <path d="M10 14L21 3"/>
                <path d="M5 7v14h14v-5"/>
            </svg>
            打开链接
        </div>
    </div>
    <div id="input-container">
        <button id="settings-button">
            <svg viewBox="0 0 24 24">
                <circle cx="12" cy="6" r="2"/>
                <circle cx="12" cy="12" r="2"/>
                <circle cx="12" cy="18" r="2"/>
            </svg>
        </button>
        <button id="webpage-qa-button" class="webpage-qa-off">
            <svg viewBox="0 0 24 24">
                <!-- 网页文档图标 -->
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" fill="none" stroke="currentColor" stroke-width="1.5"/>
                <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="1.5" fill="none"/>
                <!-- 问号图标 -->
                <circle cx="16" cy="16" r="6" fill="currentColor" opacity="0.9"/>
                <path d="M14.5 14.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5c0 .5-.25.94-.64 1.21L16 16.5v.5" stroke="white" stroke-width="1.2" stroke-linecap="round" fill="none"/>
                <circle cx="16" cy="18.5" r="0.5" fill="white"/>
            </svg>
        </button>
        <button id="related-tabs-button" class="related-tabs-off" title="相关标签页">
            <svg viewBox="0 0 24 24">
                <!-- 主文档图标，与网页问答按钮风格一致 -->
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" fill="none" stroke="currentColor" stroke-width="1.5"/>
                <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="1.5" fill="none"/>
                <!-- 第二个文档图标（稍微偏移） -->
                <path d="M16 4H8a1 1 0 0 0-1 1v2" fill="none" stroke="currentColor" stroke-width="1.2" opacity="0.6"/>
                <!-- 连接图标 -->
                <circle cx="16" cy="16" r="6" fill="currentColor" opacity="0.9"/>
                <path d="M13 16h6M16 13v6" stroke="white" stroke-width="1.2" stroke-linecap="round"/>
            </svg>
            <span id="related-tabs-count" class="tab-count" style="display: none;">0</span>
        </button>
        <div id="settings-menu">
            <div class="menu-item" id="theme-setting">
                <span>主题模式</span>
                <select id="theme-select">
                    <option value="light">浅色</option>
                    <option value="dark">深色</option>
                    <option value="auto">跟随系统</option>
                </select>
            </div>

            <div class="menu-item" id="font-size-setting">
                <span>字体大小</span>
                <select id="font-size-select">
                    <option value="small">小</option>
                    <option value="medium" selected>中</option>
                    <option value="large">大</option>
                    <option value="extra-large">特大</option>
                </select>
            </div>
            <div class="menu-item" id="new-chat">
                <span>新的对话</span>
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M8 3V13M3 8H13" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                </svg>
            </div>
            <div class="menu-item" id="chat-list">
                <span>对话列表</span>
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M2 4h12M2 8h12M2 12h12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                </svg>
            </div>
            <div class="menu-item" id="api-settings-toggle">
                <span>API 设置</span>
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M6 12L10 8L6 4" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
            </div>

        </div>
        <div id="message-input" contenteditable="true" placeholder="输入消息..." role="textbox"></div>
    </div>
    <div id="api-settings">
        <div class="settings-header">
            <button class="back-button">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path d="M12 4L6 10L12 16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
            </button>
            <span class="settings-title">API 设置</span>
        </div>
        <div class="api-cards">
            <!-- 卡片模板 -->
            <div class="api-card template" style="display: none;">
                <div class="card-content">
                    <div class="api-form">
                        <div class="form-group">
                            <div class="form-group-header">
                                <label>API Key</label>
                                <div class="card-actions">
                                    <button class="card-button duplicate-btn">
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                            <path d="M13 5H7V11H13V5Z" stroke="currentColor" stroke-width="1.5"/>
                                            <path d="M10 3H4V9" stroke="currentColor" stroke-width="1.5"/>
                                        </svg>
                                    </button>
                                    <button class="card-button delete-btn">
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                            <path d="M3 4H13" stroke="currentColor" stroke-width="1.5"/>
                                            <path d="M5 4V12H11V4" stroke="currentColor" stroke-width="1.5"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <input type="text" class="api-key" placeholder="输入 API Key">
                        </div>
                        <div class="form-group">
                            <label>Base URL</label>
                            <input type="text" class="base-url" placeholder="输入 Base URL">
                        </div>
                        <div class="form-group">
                            <label>Model Name</label>
                            <input type="text" class="model-name" placeholder="输入模型名称">
                        </div>
                        <div class="advanced-settings">
                            <div class="advanced-settings-header" tabindex="0">
                                <span>高级设置</span>
                                <span class="toggle-icon">▼</span>
                            </div>
                            <div class="advanced-settings-content" style="display: none;">
                                <div class="setting-item">
                                    <label for="system-prompt">系统提示</label>
                                    <textarea id="system-prompt" class="system-prompt" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="chat-list-page">
        <div class="settings-header">
            <button class="back-button">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path d="M12 4L6 10L12 16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
            </button>
            <span class="settings-title">对话列表</span>
        </div>
        <div class="chat-cards">
            <!-- 对话卡片模板 -->
            <div class="chat-card template" style="display: none;">
                <div class="card-content">
                    <div class="chat-info">
                        <div class="chat-title">新对话</div>
                        <div class="chat-source"></div>
                    </div>
                    <div class="card-actions">
                        <button class="card-button delete-btn">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                <path d="M3 4H13" stroke="currentColor" stroke-width="1.5"/>
                                <path d="M5 4V12H11V4" stroke="currentColor" stroke-width="1.5"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="image-preview-modal">
        <div class="image-preview-content">
            <img src="" alt="预览图片">
            <button class="image-preview-close">
                <svg viewBox="0 0 24 24" fill="none">
                    <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-linecap="round"/>
                </svg>
            </button>
        </div>
    </div>
    <div id="related-tabs-modal" class="related-tabs-modal" style="display: none;">
        <div class="related-tabs-content">
            <div class="related-tabs-header">
                <h3>相关标签页</h3>
                <button class="related-tabs-close">
                    <svg viewBox="0 0 24 24" fill="none">
                        <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-linecap="round"/>
                    </svg>
                </button>
            </div>
            <div class="related-tabs-status">
                <div id="related-tabs-loading" style="display: none;">
                    <div class="loading-spinner"></div>
                    <span>正在分析相关页面...</span>
                </div>
                <div id="related-tabs-controls" class="related-tabs-controls" style="display: none;">
                    <button id="select-all-tabs" class="select-all-btn">
                        <svg viewBox="0 0 16 16" width="14" height="14">
                            <path d="M2 8l3 3 7-7" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        全选
                    </button>
                    <button id="deselect-all-tabs" class="select-all-btn">
                        <svg viewBox="0 0 16 16" width="14" height="14">
                            <path d="M12 4L4 12M4 4l8 8" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                        取消全选
                    </button>
                </div>
                <div id="related-tabs-list" class="related-tabs-list">
                    <!-- 相关标签页列表将在这里动态生成 -->
                </div>
                <div id="related-tabs-empty" style="display: none;">
                    <p>未找到相关的标签页</p>
                </div>
            </div>
            <div class="related-tabs-actions">
                <button id="related-tabs-confirm" class="btn-primary" disabled>
                    确认使用 (<span id="selected-tabs-count">0</span> 个页面)
                </button>
                <button id="related-tabs-cancel" class="btn-secondary">取消</button>
            </div>
        </div>
    </div>
    <script src="src/main.js" type="module"></script>
</body>
</html>