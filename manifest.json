{"manifest_version": 2, "name": "Cerebr-FF", "version": "2.3.34", "description": "Cerebr - 智能AI聊天助手", "content_security_policy": "script-src 'self' 'unsafe-eval'; object-src 'self'", "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "permissions": ["storage", "activeTab", "tabs", "webRequest", "unlimitedStorage", "<all_urls>", "clipboardWrite"], "browser_action": {"default_title": "打开 Cerebr 侧边栏", "default_icon": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "default_popup": "index.html"}, "commands": {"toggle_sidebar": {"suggested_key": {"default": "Alt+Z", "windows": "Alt+Z", "mac": "MacCtrl+Z"}, "description": "打开/关闭 Cerebr 侧边栏"}, "new_chat": {"suggested_key": {"default": "Alt+X", "windows": "Alt+X", "mac": "MacCtrl+X"}, "description": "创建新的对话"}}, "background": {"scripts": ["background.js"]}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["lib/pdf.js", "content.js"], "run_at": "document_start"}], "web_accessible_resources": ["index.html", "src/main.js", "styles/main.css", "htmd/marked.min.js", "htmd/highlight.min.js", "htmd/mathjax-config.js", "htmd/tex-chtml-full.js", "lib/pdf.js", "lib/pdf.worker.js", "htmd/mermaid.min.js", "htmd/mermaid-init.js", "htmd/fonts/woff-v2/MathJax_AMS-Regular.woff", "htmd/fonts/woff-v2/MathJax_Calligraphic-Regular.woff", "htmd/fonts/woff-v2/MathJax_Fraktur-Bold.woff", "htmd/fonts/woff-v2/MathJax_Fraktur-Regular.woff", "htmd/fonts/woff-v2/MathJax_Main-Bold.woff", "htmd/fonts/woff-v2/MathJax_Main-Regular.woff", "htmd/fonts/woff-v2/MathJax_Math-BoldItalic.woff", "htmd/fonts/woff-v2/MathJax_Math-Italic.woff", "htmd/fonts/woff-v2/MathJax_Size1-Regular.woff", "htmd/fonts/woff-v2/MathJax_Size2-Regular.woff", "htmd/fonts/woff-v2/MathJax_Size3-Regular.woff", "htmd/fonts/woff-v2/MathJax_Size4-Regular.woff", "htmd/fonts/woff-v2/MathJax_Typewriter-Regular.woff", "htmd/fonts/woff-v2/MathJax_Vector-Bold.woff", "htmd/fonts/woff-v2/MathJax_Vector-Regular.woff", "htmd/fonts/woff-v2/MathJax_Zero.woff", "statics/image.png"], "browser_specific_settings": {"gecko": {"id": "<EMAIL>"}}}