/* 输入区域样式 */
#input-container {
    padding: 0;
    background-color: var(--cerebr-input-bg);
    display: flex;
    align-items: flex-start;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    flex-shrink: 0;
    padding-bottom: env(safe-area-inset-bottom);
    z-index: 100;
    min-height: 48px;
}

#message-input {
    flex: 1;
    padding: 12px;
    border: none;
    background-color: transparent;
    color: var(--cerebr-text-color);
    font-size: var(--cerebr-font-size);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    outline: none;
    resize: none;
    box-sizing: border-box;
    min-height: 24px;
    max-height: 200px;
    line-height: 1.5;
    white-space: pre-wrap;
    word-break: break-word;
    overflow-wrap: break-word;
    overflow-y: auto;
}

#message-input:empty::before {
    content: attr(placeholder);
    color: var(--cerebr-text-color);
    opacity: 0.5;
    cursor: text;
    font-family: '<PERSON>lo', 'Monaco', 'Courier New', monospace;
}

#message-input:focus {
    outline: none;
}

#message-input br {
    display: none;
}

#message-input br:first-child {
    display: block;
}