/* 代码相关的CSS变量 */
:root {
    /* 代码块变量 - 浅色模式 */
    --cerebr-code-block-bg: #f6f8fa;
    --cerebr-code-block-color: #24292e;
    --cerebr-code-block-border: #e1e4e8;
    --cerebr-code-scrollbar-bg: #f6f8fa;
    --cerebr-code-scrollbar-thumb: #d1d5da;
    --cerebr-code-scrollbar-thumb-hover: #a5a9ad;
    --cerebr-code-tag-bg: #f6f8fa;
    --cerebr-code-tag-color: #57606a;
    --cerebr-pre-bg: #0d1117;

    /* 语法高亮 - 浅色模式 */
    --cerebr-hljs-keyword: #d73a49;
    --cerebr-hljs-built-in: #005cc5;
    --cerebr-hljs-type: #d73a49;
    --cerebr-hljs-literal: #005cc5;
    --cerebr-hljs-number: #005cc5;
    --cerebr-hljs-regexp: #032f62;
    --cerebr-hljs-string: #032f62;
    --cerebr-hljs-comment: #6a737d;
    --cerebr-hljs-doctag: #d73a49;
    --cerebr-hljs-meta: #6a737d;
    --cerebr-hljs-title: #6f42c1;
    --cerebr-hljs-tag: #22863a;
    --cerebr-hljs-attr: #005cc5;
    --cerebr-hljs-attribute: #005cc5;
    --cerebr-hljs-template-tag: #d73a49;
    --cerebr-hljs-template-variable: #d73a49;
    --cerebr-hljs-variable: #d73a49;
    --cerebr-hljs-bullet: #735c0f;
    --cerebr-hljs-emphasis: #24292e;
    --cerebr-hljs-strong: #24292e;
}

/* 深色主题变量 */
:root.dark-theme {
    /* 代码块变量 - 深色模式 */
    --cerebr-code-block-bg: #282c34;
    --cerebr-code-block-color: #abb2bf;
    --cerebr-code-block-border: transparent;
    --cerebr-code-scrollbar-bg: #282c34;
    --cerebr-code-scrollbar-thumb: #4b5263;
    --cerebr-code-scrollbar-thumb-hover: #5c6370;
    --cerebr-code-tag-bg: #161b22;
    --cerebr-code-tag-color: #7d8590;
    --cerebr-pre-bg: #1e222a;

    /* 语法高亮 - 深色模式 */
    --cerebr-hljs-keyword: #c678dd;
    --cerebr-hljs-built-in: #d19a66;
    --cerebr-hljs-type: #c678dd;
    --cerebr-hljs-literal: #d19a66;
    --cerebr-hljs-number: #d19a66;
    --cerebr-hljs-regexp: #98c379;
    --cerebr-hljs-string: #98c379;
    --cerebr-hljs-comment: #7f848e;
    --cerebr-hljs-doctag: #c678dd;
    --cerebr-hljs-meta: #7f848e;
    --cerebr-hljs-title: #61afef;
    --cerebr-hljs-tag: #e06c75;
    --cerebr-hljs-attr: #d19a66;
    --cerebr-hljs-attribute: #d19a66;
    --cerebr-hljs-template-tag: #c678dd;
    --cerebr-hljs-template-variable: #c678dd;
    --cerebr-hljs-variable: #c678dd;
    --cerebr-hljs-bullet: #d19a66;
    --cerebr-hljs-emphasis: #abb2bf;
    --cerebr-hljs-strong: #abb2bf;
}

/* 系统深色主题 */
@media (prefers-color-scheme: dark) {
    :root:not(.light-theme) {
        /* 代码块变量 - 系统深色模式 */
        --cerebr-code-block-bg: #282c34;
        --cerebr-code-block-color: #abb2bf;
        --cerebr-code-block-border: transparent;
        --cerebr-code-scrollbar-bg: #282c34;
        --cerebr-code-scrollbar-thumb: #4b5263;
        --cerebr-code-scrollbar-thumb-hover: #5c6370;
        --cerebr-code-tag-bg: #161b22;
        --cerebr-code-tag-color: #7d8590;
        --cerebr-pre-bg: #1e222a;

        /* 语法高亮 - 系统深色模式 */
        --cerebr-hljs-keyword: #c678dd;
        --cerebr-hljs-built-in: #d19a66;
        --cerebr-hljs-type: #c678dd;
        --cerebr-hljs-literal: #d19a66;
        --cerebr-hljs-number: #d19a66;
        --cerebr-hljs-regexp: #98c379;
        --cerebr-hljs-string: #98c379;
        --cerebr-hljs-comment: #7f848e;
        --cerebr-hljs-doctag: #c678dd;
        --cerebr-hljs-meta: #7f848e;
        --cerebr-hljs-title: #61afef;
        --cerebr-hljs-tag: #e06c75;
        --cerebr-hljs-attr: #d19a66;
        --cerebr-hljs-attribute: #d19a66;
        --cerebr-hljs-template-tag: #c678dd;
        --cerebr-hljs-template-variable: #c678dd;
        --cerebr-hljs-variable: #c678dd;
        --cerebr-hljs-bullet: #d19a66;
        --cerebr-hljs-emphasis: #abb2bf;
        --cerebr-hljs-strong: #abb2bf;
    }
}

/* 代码块基础样式 */
pre {
    margin: 12px 0;
    position: relative;
    border-radius: 8px;
    background-color: var(--cerebr-pre-bg);
    overflow: hidden;
}

pre code {
    display: block;
    padding: 16px;
    overflow-x: auto;
    font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    /* line-height: 1.5; */
    line-height: 1rem;
    tab-size: 4;
    -webkit-font-smoothing: auto;
    border-radius: 8px;
    background-color: var(--cerebr-code-block-bg);
    color: var(--cerebr-code-block-color);
    border: 1px solid var(--cerebr-code-block-border);
}

/* Markdown 样式 */
pre code {
    display: block;
    overflow-x: auto;
    padding: 1em;
    border-radius: 6px;
    font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
    white-space: pre;
    /* 浅色模式默认样式 */
    background: var(--cerebr-code-block-bg);
    color: var(--cerebr-code-block-color);
    border: 1px solid var(--cerebr-code-block-border);
}

/* 行内代码样式 */
code:not(pre code) {
    padding: 2px 6px;
    margin: 0 2px;
    border-radius: 4px;
    font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
    font-size: 0.9em;
    background-color: var(--cerebr-inline-code-bg);
    color: var(--cerebr-text-color);
}

/* 所有代码样式 */
code {
    background: var(--cerebr-button-hover-bg);
    padding: 2px 4px;
    border-radius: 4px;
    font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
}

/* 滚动条样式 */
pre code::-webkit-scrollbar {
    height: 8px;
    background-color: var(--cerebr-code-scrollbar-bg);
}

pre code::-webkit-scrollbar-thumb {
    background-color: var(--cerebr-code-scrollbar-thumb);
    border-radius: 4px;
}

pre code::-webkit-scrollbar-thumb:hover {
    background-color: var(--cerebr-code-scrollbar-thumb-hover);
}

/* 语言标签 */
pre::before {
    content: attr(data-language);
    position: absolute;
    top: 1px;
    right: 1px;
    padding: 4px 8px;
    font-size: 12px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: var(--cerebr-code-tag-color);
    background-color: var(--cerebr-code-tag-bg);
    border-bottom-left-radius: 8px;
    opacity: 0.5;
}

/* 为所有的语法高亮元素添加统一字体 */
pre code [class^="hljs-"] {
    font-family: "JetBrains Mono", Monaco, Consolas, "Andale Mono", "DejaVu Sans Mono", monospace;
}

/* 浅色模式代码高亮 */
pre code .hljs-keyword {
    color: var(--cerebr-hljs-keyword);
}

pre code .hljs-built_in {
    color: var(--cerebr-hljs-built-in);
}

pre code .hljs-type {
    color: var(--cerebr-hljs-type);
}

pre code .hljs-literal {
    color: var(--cerebr-hljs-literal);
}

pre code .hljs-number {
    color: var(--cerebr-hljs-number);
}

pre code .hljs-regexp {
    color: var(--cerebr-hljs-regexp);
}

pre code .hljs-string {
    color: var(--cerebr-hljs-string);
}

pre code .hljs-comment {
    color: var(--cerebr-hljs-comment);
}

pre code .hljs-doctag {
    color: var(--cerebr-hljs-doctag);
}

pre code .hljs-meta {
    color: var(--cerebr-hljs-meta);
}

pre code .hljs-title {
    color: var(--cerebr-hljs-title);
}

pre code .hljs-tag {
    color: var(--cerebr-hljs-tag);
}

pre code .hljs-attr {
    color: var(--cerebr-hljs-attr);
}

pre code .hljs-attribute {
    color: var(--cerebr-hljs-attribute);
}

pre code .hljs-template-tag {
    color: var(--cerebr-hljs-template-tag);
}

pre code .hljs-template-variable {
    color: var(--cerebr-hljs-template-variable);
}

pre code .hljs-variable {
    color: var(--cerebr-hljs-variable);
}

pre code .hljs-bullet {
    color: var(--cerebr-hljs-bullet);
}

pre code .hljs-emphasis {
    color: var(--cerebr-hljs-emphasis);
    font-style: italic;
}

pre code .hljs-strong {
    color: var(--cerebr-hljs-strong);
    font-weight: bold;
}