/* 聊天列表页面样式 */
#chat-list-page {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    display: none;
    flex-direction: column;
    background: var(--cerebr-bg-color);
}

#chat-list-page.show {
    display: flex;
}

.chat-cards {
    padding: 16px;
    flex: 1;
    overflow-y: auto;
}

.chat-card {
    outline: none;
    cursor: pointer;
    border-radius: 8px;
    position: relative;
    margin-bottom: 12px;
    background: var(--cerebr-message-ai-bg);
    border: 1px solid var(--cerebr-card-border-color);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.chat-card:hover,
.chat-card:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px var(--cerebr-card-border-color);
}

.chat-card.selected {
    border-color: var(--cerebr-highlight-border-color);
    box-shadow: 0 0 0 1px var(--cerebr-highlight-border-color);
}

.chat-card .card-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
}

.chat-card .chat-info {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    margin-right: 12px;
    min-width: 0;
}

.chat-card .chat-title {
    font-size: 14px;
    color: var(--cerebr-text-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chat-card .chat-source {
    font-size: 12px;
    color: var(--cerebr-link-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-top: 4px;
}

.chat-card .chat-source a {
    color: var(--cerebr-link-color);
    display: block;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-decoration: none;
}

.chat-card .card-actions {
    display: flex;
    gap: 8px;
    z-index: 3;
}

.chat-card .card-button {
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
    color: var(--cerebr-text-color);
    opacity: 0.6;
    transition: opacity 0.2s, background-color 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    position: relative;
}

.chat-card .card-button:hover {
    opacity: 1;
    background-color: var(--cerebr-button-hover-bg);
}

.chat-card .card-button svg {
    width: 16px;
    height: 16px;
    pointer-events: none;
    stroke: currentColor;
    fill: none;
    stroke-width: 1.5;
}