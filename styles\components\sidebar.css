/* 侧边栏基础样式 */
.cerebr-sidebar {
    position: fixed;
    top: 20px;
    right: -450px;
    width: 430px;
    height: calc(100vh - 40px);
    background: var(--cerebr-bg-color);
    color: var(--cerebr-text-color);
    box-shadow: var(--cerebr-sidebar-box-shadow);
    z-index: 2147483647;
    border-radius: 12px;
    margin-right: 20px;
    overflow: hidden;
    visibility: hidden;
    
    pointer-events: none;
    contain: style layout size;
    isolation: isolate;
}

.cerebr-sidebar.initialized {
    visibility: visible;
    transition: transform 0.3s ease;
    pointer-events: auto;
}

.cerebr-sidebar.visible {
    transform: translateX(-450px);
}

/* 侧边栏组件样式 */
.cerebr-sidebar__header {
    height: 40px;
    background: #f5f5f5;
    border-bottom: 1px solid #ddd;
    display: flex;
    align-items: center;
    padding: 0 15px;
}

.cerebr-sidebar__resizer {
    position: absolute;
    left: 0;
    top: 0;
    width: 5px;
    height: 100%;
    cursor: ew-resize;
}

.cerebr-sidebar__content {
    height: 100%;
    overflow: hidden;
    border-radius: 12px;
    contain: style layout size;
}

.cerebr-sidebar__iframe {
    width: 100%;
    height: 100%;
    border: none;
    background: var(--cerebr-bg-color);
    contain: strict;
}