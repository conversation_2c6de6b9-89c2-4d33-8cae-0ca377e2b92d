{"it/messages/alphabets.min": {"kind": "alphabets", "locale": "it", "messages": {"latinSmall": ["a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z"], "latinCap": ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"], "greekSmall": ["nabla", "alfa", "beta", "gamma", "delta", "epsilon", "zeta greca", "eta", "theta", "iota", "kappa greca", "lambda", "mu", "nu", "xi", "omicron", "pi greco", "rho", "sigma final", "sigma", "tau", "upsilon", "phi", "chi", "psi", "omega", "derivata parziale", "epsilon", "theta", "kappa", "phi", "rho", "pi"], "greekCap": ["Alfa", "Beta", "Gamma", "Delta", "Epsilon", "Zeta greca", "Eta", "Theta", "Iota", "Kappa greca", "Lambda", "Mu", "<PERSON>u", "Xi", "Omicron", "Pi greca", "Rho", "Theta", "Sigma", "Tau", "Upsilon", "Phi", "<PERSON>", "Psi", "Omega"], "capPrefix": {"default": "<PERSON><PERSON><PERSON>"}, "smallPrefix": {"default": ""}, "digitPrefix": {"default": ""}}}, "it/messages/messages.min": {"kind": "messages", "locale": "it", "messages": {"MS": {"START": "inizio", "FRAC_V": "frazione", "FRAC_B": "frazione", "FRAC_S": "frazione", "END": "fine", "FRAC_OVER": "fratto", "ONCE": "una volta", "TWICE": "due volte", "NEST_FRAC": "annidamento", "ENDFRAC": "fine frazione", "SUPER": "super", "SUB": "sub", "SUP": "sup", "SUPERSCRIPT": "apice", "SUBSCRIPT": "pedice", "BASELINE": "linea di base", "BASE": "base", "NESTED": "anni<PERSON><PERSON>", "NEST_ROOT": "annidamento", "STARTROOT": "inizio radice", "ENDROOT": "fine radice", "ROOTINDEX": "indice radice", "ROOT": "radice", "INDEX": "indice Radice", "UNDER": "sotto", "UNDERSCRIPT": "sottos<PERSON>ritto", "OVER": "sopra", "OVERSCRIPT": "sopra<PERSON><PERSON><PERSON><PERSON>", "ENDSCRIPTS": "fine script"}, "MSroots": {}, "font": {"bold": "grassetto", "bold-fraktur": "grassetto gotico", "bold-italic": "grassetto italico", "bold-script": "grassetto script", "caligraphic": "calligrafico", "caligraphic-bold": "grassetto calligrafico", "double-struck": "grassetto da lavagna", "double-struck-italic": "grassetto da lavagna italico", "fraktur": "gotico", "fullwidth": "fullwidth", "italic": "italico", "monospace": "monospazio", "normal": "normale", "oldstyle": "vecchio stile", "oldstyle-bold": "vecchio stile grassetto", "script": "script", "sans-serif": "senza grazie", "sans-serif-italic": "senza grazie italico", "sans-serif-bold": "senza grazie grassetto", "sans-serif-bold-italic": "senza grazie grassetto italico", "unknown": "scon<PERSON><PERSON><PERSON>"}, "embellish": {"super": ["apice", "prefixCombiner"], "sub": ["pedice", "prefixCombiner"], "circled": ["cerchiato", "italianPostfix"], "parenthesized": "tra <PERSON>esi", "period": "punto", "negative-circled": ["cerchiato in negativo", "italianPostfix"], "double-circled": "do<PERSON>io cerchiato", "circled-sans-serif": "cerchiato senza grazie", "negative-circled-sans-serif": "cerchiato in negativo senza grazie", "comma": "virgola", "squared": "dentro quadrato", "negative-squared": "dentro quadrato in negativo"}, "role": {"addition": "addizione", "multiplication": "moltiplicazione", "subtraction": "sottrazione", "division": "divisione", "equality": "uguaglianza", "inequality": "disuguaglianza", "element": "elemento", "arrow": "freccia", "determinant": "determinante", "rowvector": "vettore riga", "binomial": "binomiale", "squarematrix": "matrice quadrata", "set empty": "insieme vuoto", "set extended": "estensione di insieme", "set singleton": "sing<PERSON>tto", "set collection": "collezione", "label": "<PERSON><PERSON><PERSON>", "multiline": "linee multiple", "matrix": "matrice", "vector": "vettore", "cases": "comando switch", "table": "tavola", "unknown": "scon<PERSON><PERSON><PERSON>"}, "enclose": {"longdiv": "divisione lunga", "actuarial": "simbolo attuario", "radical": "radice quadrata", "box": "riquadro", "roundedbox": "riquadro arrotondato", "circle": "cerchio", "left": "linea verticale sinistra", "right": "linea verticale destra", "top": "barra sopra", "bottom": "barra sotto", "updiagonalstrike": "cancellatura", "downdiagonalstrike": "cancellatura", "verticalstrike": "cancellatura verticale", "horizontalstrike": "cancellatura", "madruwb": "simbolo fattoriale arabo", "updiagonalarrow": "freccia diagonale", "phasorangle": "angolo fasore", "unknown": "divisione lunga"}, "navigate": {"COLLAPSIBLE": "collassabile", "EXPANDABLE": "espandibile", "LEVEL": "livello"}, "regexp": {"TEXT": "a-zA-ZàèìòùéóÀ", "NUMBER": "((\\d{1,3})(?=(.| ))((.| )\\d{3})*(\\,\\d+)?)|^\\d*\\,\\d+|^\\d+", "DECIMAL_MARK": ",", "DIGIT_GROUP": "\\.", "JOINER_SUBSUPER": " ", "JOINER_FRAC": " "}, "unitTimes": ""}}, "it/messages/numbers.min": {"kind": "numbers", "locale": "it", "messages": {"zero": "zero", "ones": ["", "uno", "due", "tre", "quattro", "cinque", "sei", "sette", "otto", "nove", "dieci", "undici", "dodici", "tredici", "qua<PERSON><PERSON><PERSON>", "quindici", "sedici", "diciassette", "<PERSON><PERSON><PERSON>", "diciannove"], "tens": ["", "", "venti", "trenta", "quaranta", "cinquanta", "sessanta", "<PERSON><PERSON><PERSON>", "ottanta", "<PERSON>vanta"], "large": ["", "mille", "milione", "milia<PERSON>", "bilione", "biliardo", "trilione", "triliardo", "quadrilione", "quadriliardo", "quntilione", "quintil<PERSON><PERSON>"], "special": {"onesOrdinals": ["zero", "primo", "secondo", "terzo", "quarto", "quinto", "sesto", "setti<PERSON>", "ottavo", "nono", "decimo"]}, "vulgarSep": " ", "numSep": ""}}, "it/si/prefixes.min": [{"a": "atto", "c": "centi", "da": "deca", "d": "deci", "E": "exa", "f": "femto", "G": "giga", "h": "etto", "k": "chilo", "M": "mega", "m": "milli", "n": "nano", "P": "peta", "p": "pico", "T": "tera", "Y": "yotta", "y": "yocto", "z": "zepto", "Z": "zetta", "µ": "micro", "μ": "micro"}], "it/functions/algebra.min": [{"locale": "it"}, {"locale": "it"}, {"key": "deg", "category": "Algebra", "names": ["deg"], "mappings": {"default": {"default": "grado"}}}, {"key": "det", "mappings": {"default": {"default": "determinante"}}, "category": "Algebra", "names": ["det"]}, {"key": "dim", "category": "Algebra", "names": ["dim"], "mappings": {"default": {"default": "dimensione"}}}, {"key": "hom", "category": "Algebra", "names": ["hom", "Hom"], "mappings": {"default": {"default": "omomorfismo"}}}, {"key": "ker", "category": "Algebra", "names": ["ker"], "mappings": {"default": {"default": "nucleo"}}}, {"key": "Tr", "category": "Algebra", "names": ["Tr", "tr"], "mappings": {"default": {"default": "traccia"}}}], "it/functions/elementary.min": [{"locale": "it"}, {"key": "log", "mappings": {"default": {"default": "logaritmo"}}, "category": "Logarithm", "names": ["log"]}, {"key": "ln", "mappings": {"default": {"default": "logaritmo naturale"}, "clearspeak": {"default": "l n", "Log_LnAsNaturalLog": "logaritmo naturale"}}, "category": "Logarithm", "names": ["ln"]}, {"key": "lg", "category": "Logarithm", "names": ["lg"], "mappings": {"default": {"default": "logaritmo in basi 10"}}}, {"key": "exp", "category": "Elementary", "names": ["exp", "expt"], "mappings": {"default": {"default": "esponenziale"}}}, {"key": "gcd", "category": "Elementary", "names": ["gcd", "mcd", "MCD"], "mappings": {"default": {"default": "massimo comun divisore"}}}, {"key": "lcm", "category": "Elementary", "names": ["lcm", "mcm", "MCM"], "mappings": {"default": {"default": "minimo comune multiplo"}}}, {"key": "arg", "category": "Complex", "names": ["arg"], "mappings": {"default": {"default": "argomento"}}}, {"key": "im", "mappings": {"default": {"default": "la parte immaginaria del numero complesso"}}, "category": "Complex", "names": ["im"]}, {"key": "re", "mappings": {"default": {"default": "la parte reale del numero complesso"}}, "category": "Complex", "names": ["re"]}, {"key": "inf", "category": "Limits", "names": ["inf"], "mappings": {"default": {"default": "estremo inferiore"}}}, {"key": "lim", "mappings": {"default": {"default": "limite"}}, "category": "Limits", "names": ["lim"]}, {"key": "liminf", "category": "Limits", "names": ["lim inf", "liminf"], "mappings": {"default": {"default": "limite inferiore"}}}, {"key": "limsup", "category": "Limits", "names": ["lim sup", "limsup"], "mappings": {"default": {"default": "limite superiore"}}}, {"key": "max", "category": "Limits", "names": ["max"], "mappings": {"default": {"default": "massimo"}}}, {"key": "min", "category": "Limits", "names": ["min"], "mappings": {"default": {"default": "minimo"}}}, {"key": "sup", "category": "Limits", "names": ["sup"], "mappings": {"default": {"default": "estremo superiore"}}}, {"key": "<PERSON><PERSON><PERSON>", "category": "Limits", "names": ["<PERSON><PERSON><PERSON>", "inj lim"], "mappings": {"default": {"default": "colimiti"}}}, {"key": "proj<PERSON>", "category": "Limits", "names": ["proj<PERSON>", "proj lim"], "mappings": {"default": {"default": "limite proiettivo"}}}, {"key": "mod", "mappings": {"default": {"default": "modulo"}}, "category": "Elementary", "names": ["mod"]}, {"key": "Pr", "category": "Probability", "names": ["Pr"], "mappings": {"default": {"default": "probabilità"}}}], "it/functions/hyperbolic.min": [{"locale": "it"}, {"key": "cosh", "mappings": {"default": {"default": "coseno <PERSON>o"}}, "category": "Hyperbolic", "names": ["cosh"]}, {"key": "coth", "mappings": {"default": {"default": "cotangente iperbolica"}}, "category": "Hyperbolic", "names": ["coth"]}, {"key": "csch", "mappings": {"default": {"default": "cosecante iperbolica"}}, "category": "Hyperbolic", "names": ["csch"]}, {"key": "sech", "mappings": {"default": {"default": "secante i<PERSON>a"}}, "category": "Hyperbolic", "names": ["sech"]}, {"key": "sinh", "mappings": {"default": {"default": "seno iper<PERSON>o"}}, "category": "Hyperbolic", "names": ["sinh"]}, {"key": "tanh", "mappings": {"default": {"default": "tangente iperbolica"}}, "category": "Hyperbolic", "names": ["tanh"]}, {"key": "arcosh", "mappings": {"default": {"default": "settore coseno i<PERSON>o"}}, "category": "Area", "names": ["arcosh", "arccosh", "settcosh"]}, {"key": "arcoth", "mappings": {"default": {"default": "settore co-tangente iperbolica"}}, "category": "Area", "names": ["arcoth", "arccoth", "settcoth"]}, {"key": "<PERSON><PERSON>", "mappings": {"default": {"default": "settore co-secante iper<PERSON>a"}}, "category": "Area", "names": ["<PERSON><PERSON>", "arc<PERSON>ch", "settcsch"]}, {"key": "arsech", "mappings": {"default": {"default": "settore secante iperbolica"}}, "category": "Area", "names": ["arsech", "arcsech", "settsech"]}, {"key": "a<PERSON><PERSON><PERSON>", "mappings": {"default": {"default": "settore seno iperbolico"}}, "category": "Area", "names": ["a<PERSON><PERSON><PERSON>", "arcsinh", "set<PERSON><PERSON><PERSON>"]}, {"key": "artanh", "mappings": {"default": {"default": "settore tangente iperbolica"}}, "category": "Area", "names": ["artanh", "arctanh", "settanh"]}], "it/functions/trigonometry.min": [{"locale": "it"}, {"key": "cos", "mappings": {"default": {"default": "coseno"}}, "category": "Trigonometric", "names": ["cos", "cosine"]}, {"key": "cot", "mappings": {"default": {"default": "cotangente"}}, "category": "Trigonometric", "names": ["cot"]}, {"key": "csc", "mappings": {"default": {"default": "cosecante"}}, "category": "Trigonometric", "names": ["csc"]}, {"key": "sec", "mappings": {"default": {"default": "secante"}}, "category": "Trigonometric", "names": ["sec"]}, {"key": "sin", "mappings": {"default": {"default": "seno"}}, "category": "Trigonometric", "names": ["sin", "sine", "sen"]}, {"key": "tan", "mappings": {"default": {"default": "tangente"}}, "category": "Trigonometric", "names": ["tan"]}, {"key": "arccos", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}, "category": "Cyclometric", "names": ["arccos", "acos"]}, {"key": "<PERSON><PERSON>", "mappings": {"default": {"default": "arcocotangente"}}, "category": "Cyclometric", "names": ["<PERSON><PERSON>", "acot"]}, {"key": "arccsc", "mappings": {"default": {"default": "arcocosecante"}}, "category": "Cyclometric", "names": ["arccsc", "acsc"]}, {"key": "arcsec", "mappings": {"default": {"default": "arcosecante"}}, "category": "Cyclometric", "names": ["arcsec", "asec"]}, {"key": "arcsin", "mappings": {"default": {"default": "arcoseno"}}, "category": "Cyclometric", "names": ["arcsin", "<PERSON><PERSON>", "asin", "asen", "asn"]}, {"key": "arctan", "mappings": {"default": {"default": "arcotangente"}}, "category": "Cyclometric", "names": ["arctan", "atan"]}], "it/symbols/digits_rest.min": [{"locale": "it"}, {"key": "00B2", "mappings": {"default": {"default": "apice 2"}}, "category": "No"}, {"key": "00B3", "mappings": {"default": {"default": "apice 3"}}, "category": "No"}, {"key": "00BC", "mappings": {"default": {"default": "un quarto"}}, "category": "No"}, {"key": "00BD", "mappings": {"default": {"default": "un mezzo"}}, "category": "No"}, {"key": "00BE", "mappings": {"default": {"default": "tre quarti"}}, "category": "No"}, {"category": "No", "key": "2150", "mappings": {"default": {"default": "un settimo"}}}, {"category": "No", "key": "2151", "mappings": {"default": {"default": "un nono"}}}, {"category": "No", "key": "2152", "mappings": {"default": {"default": "un decimo"}}}, {"key": "2153", "mappings": {"default": {"default": "un terzo"}}, "category": "No"}, {"key": "2154", "mappings": {"default": {"default": "due terzi"}}, "category": "No"}, {"key": "2155", "mappings": {"default": {"default": "un quinto"}}, "category": "No"}, {"key": "2156", "mappings": {"default": {"default": "due quinti"}}, "category": "No"}, {"key": "2157", "mappings": {"default": {"default": "tre quinti"}}, "category": "No"}, {"key": "2158", "mappings": {"default": {"default": "quattro quinti"}}, "category": "No"}, {"key": "2159", "mappings": {"default": {"default": "un sesto"}}, "category": "No"}, {"key": "215A", "mappings": {"default": {"default": "cinque sesti"}}, "category": "No"}, {"key": "215B", "mappings": {"default": {"default": "un ottavo"}}, "category": "No"}, {"key": "215C", "mappings": {"default": {"default": "tre ottavi"}}, "category": "No"}, {"key": "215D", "mappings": {"default": {"default": "cinque ottavi"}}, "category": "No"}, {"key": "215E", "mappings": {"default": {"default": "sette ottavi"}}, "category": "No"}, {"category": "No", "key": "215F", "mappings": {"default": {"default": "numeratore uno"}}}, {"category": "No", "key": "2189", "mappings": {"default": {"default": "zero un terzo"}}}, {"category": "No", "key": "3248", "mappings": {"default": {"default": "dieci cerchiato su quadrato nero"}}}, {"category": "No", "key": "3249", "mappings": {"default": {"default": "venti cerchiato su quadrato nero"}}}, {"category": "No", "key": "324A", "mappings": {"default": {"default": "trenta cerchiato su quadrato nero"}}}, {"category": "No", "key": "324B", "mappings": {"default": {"default": "quaranta cerchiato su quadrato nero"}}}, {"category": "No", "key": "324C", "mappings": {"default": {"default": "cinquanta cerchiato su quadrato nero"}}}, {"category": "No", "key": "324D", "mappings": {"default": {"default": "sessanta cerchiato su quadrato nero"}}}, {"category": "No", "key": "324E", "mappings": {"default": {"default": "settanta cerchiato su quadrato nero"}}}, {"category": "No", "key": "324F", "mappings": {"default": {"default": "ottanta cerchiato su quadrato nero"}}}], "it/symbols/greek-rest.min": [{"locale": "it"}, {"key": "0394", "mappings": {"clearspeak": {"default": "triangolo", "TriangleSymbol_Delta": "Delta maiuscola"}}, "category": "<PERSON>"}], "it/symbols/greek-scripts.min": [{"locale": "it"}, {"category": "Ll", "key": "1D26", "mappings": {"default": {"default": "Gamma maiuscola piccola"}}}, {"category": "Ll", "key": "1D27", "mappings": {"default": {"default": "<PERSON><PERSON> ma<PERSON>cola piccola"}}}, {"category": "Ll", "key": "1D28", "mappings": {"default": {"default": "Pi greca maiuscola piccola"}}}, {"category": "Ll", "key": "1D29", "mappings": {"default": {"default": "<PERSON><PERSON> ma<PERSON>cola piccola"}}}, {"category": "Ll", "key": "1D2A", "mappings": {"default": {"default": "Psi ma<PERSON>cola piccola"}}}, {"category": "Lm", "key": "1D5E", "mappings": {"default": {"default": "gamma apice"}}}, {"category": "Lm", "key": "1D60", "mappings": {"default": {"default": "phi apice"}}}, {"category": "Lm", "key": "1D66", "mappings": {"default": {"default": "beta apice"}}}, {"category": "Lm", "key": "1D67", "mappings": {"default": {"default": "gamma pedice"}}}, {"category": "Lm", "key": "1D68", "mappings": {"default": {"default": "rho pedice"}}}, {"category": "Lm", "key": "1D69", "mappings": {"default": {"default": "phi pedice"}}}, {"category": "Lm", "key": "1D6A", "mappings": {"default": {"default": "chi pedice"}}}], "it/symbols/greek-symbols.min": [{"locale": "it"}, {"category": "Ll", "key": "03D0", "mappings": {"default": {"default": "beta arricciata"}}}, {"category": "Ll", "key": "03D7", "mappings": {"default": {"default": "simbolo greco Kai"}}}, {"category": "Sm", "key": "03F6", "mappings": {"default": {"default": "epsilon dritto invertito"}}}, {"category": "<PERSON>", "key": "1D7CA", "mappings": {"default": {"default": "Digamma ma<PERSON>cola in grassetto"}}}, {"category": "Ll", "key": "1D7CB", "mappings": {"default": {"default": "digamma in grassetto"}}}], "it/symbols/hebrew_letters.min": [{"locale": "it"}, {"key": "2135", "mappings": {"default": {"default": "alef"}}, "category": "Lo"}, {"key": "2136", "mappings": {"default": {"default": "bet"}}, "category": "Lo"}, {"key": "2137", "mappings": {"default": {"default": "gimel"}}, "category": "Lo"}, {"key": "2138", "mappings": {"default": {"default": "dalet"}}, "category": "Lo"}], "it/symbols/latin-lower-double-accent.min": [{"locale": "it"}, {"category": "Ll", "key": "01D6", "mappings": {"default": {"default": "u con diaeresi e macron"}}}, {"category": "Ll", "key": "01D8", "mappings": {"default": {"default": "u con diaeresi e acuto"}}}, {"category": "Ll", "key": "01DA", "mappings": {"default": {"default": "u con diaeresi e caronte"}}}, {"category": "Ll", "key": "01DC", "mappings": {"default": {"default": "u con diaeresi e tomba"}}}, {"category": "Ll", "key": "01DF", "mappings": {"default": {"default": "a con diaeresis e macron"}}}, {"category": "Ll", "key": "01E1", "mappings": {"default": {"default": "a con punto e macron"}}}, {"category": "Ll", "key": "01ED", "mappings": {"default": {"default": "o con Ogonek e macron"}}}, {"category": "Ll", "key": "01FB", "mappings": {"default": {"default": "a con anello sopra e accento acuto"}}}, {"category": "Ll", "key": "022B", "mappings": {"default": {"default": "o con diaeresi e macron"}}}, {"category": "Ll", "key": "022D", "mappings": {"default": {"default": "o con tilde e macron"}}}, {"category": "Ll", "key": "0231", "mappings": {"default": {"default": "o con punto sopra e macron"}}}, {"key": "1E09", "mappings": {"default": {"default": "c con cediglia e accento acuto"}}, "category": "Ll"}, {"key": "1E15", "mappings": {"default": {"default": "e con barra sopra e accento grave"}}, "category": "Ll"}, {"key": "1E17", "mappings": {"default": {"default": "e con barra sopra e accento acuto"}}, "category": "Ll"}, {"key": "1E1D", "mappings": {"default": {"default": "e con cediglia e con segno di vocale corta"}}, "category": "Ll"}, {"key": "1E2F", "mappings": {"default": {"default": "i con dieresi ed accento acuto"}}, "category": "Ll"}, {"key": "1E39", "mappings": {"default": {"default": "l con punto sotto e con barra sopra"}}, "category": "Ll"}, {"key": "1E4D", "mappings": {"default": {"default": "o con tilde ed accento acuto"}}, "category": "Ll"}, {"key": "1E4F", "mappings": {"default": {"default": "o con tilde e dieresi"}}, "category": "Ll"}, {"key": "1E51", "mappings": {"default": {"default": "o con barra sopra ed accento grave"}}, "category": "Ll"}, {"key": "1E53", "mappings": {"default": {"default": "o con barra sopra ed accento acuto"}}, "category": "Ll"}, {"key": "1E5D", "mappings": {"default": {"default": "r con punto sotto e con barra sopra"}}, "category": "Ll"}, {"key": "1E65", "mappings": {"default": {"default": "s con accento acuto e punto sopra"}}, "category": "Ll"}, {"key": "1E67", "mappings": {"default": {"default": "s con caron e punto sopra"}}, "category": "Ll"}, {"key": "1E69", "mappings": {"default": {"default": "s con punto sotto e punto sopra"}}, "category": "Ll"}, {"key": "1E79", "mappings": {"default": {"default": "u con tilde ed accento acuto"}}, "category": "Ll"}, {"key": "1E7B", "mappings": {"default": {"default": "u con barra sopra e dieresi"}}, "category": "Ll"}, {"key": "1EA5", "mappings": {"default": {"default": "a con accento circonflesso ed accento acuto"}}, "category": "Ll"}, {"key": "1EA7", "mappings": {"default": {"default": "a con accento circonflesso ed accento grave"}}, "category": "Ll"}, {"key": "1EA9", "mappings": {"default": {"default": "a con accento circonflesso e gancio sopra"}}, "category": "Ll"}, {"key": "1EAB", "mappings": {"default": {"default": "a con accento circonflesso e tilde"}}, "category": "Ll"}, {"key": "1EAD", "mappings": {"default": {"default": "a con accento circonflesso e punto sotto"}}, "category": "Ll"}, {"key": "1EAF", "mappings": {"default": {"default": "a con segno di vocale corta ed accento acuto"}}, "category": "Ll"}, {"key": "1EB1", "mappings": {"default": {"default": "a con segno di vocale corta ed accento grave"}}, "category": "Ll"}, {"key": "1EB3", "mappings": {"default": {"default": "a con segno di vocale corta e gancio sopra"}}, "category": "Ll"}, {"key": "1EB5", "mappings": {"default": {"default": "a con segno di vocale corta e tilde"}}, "category": "Ll"}, {"key": "1EB7", "mappings": {"default": {"default": "a con segno di vocale corta e punto sotto"}}, "category": "Ll"}, {"key": "1EBF", "mappings": {"default": {"default": "e con accento circonflesso ed acuto"}}, "category": "Ll"}, {"key": "1EC1", "mappings": {"default": {"default": "e con accento circonflesso e grave"}}, "category": "Ll"}, {"key": "1EC3", "mappings": {"default": {"default": "e con accento circonflesso e gancio sopra"}}, "category": "Ll"}, {"key": "1EC5", "mappings": {"default": {"default": "e con accento circonflesso e tilde"}}, "category": "Ll"}, {"key": "1EC7", "mappings": {"default": {"default": "e con accento circonflesso e punto sotto"}}, "category": "Ll"}, {"key": "1ED1", "mappings": {"default": {"default": "o con accento circonflesso e accento acuto"}}, "category": "Ll"}, {"key": "1ED3", "mappings": {"default": {"default": "o con accento circonflesso e accento grave"}}, "category": "Ll"}, {"key": "1ED5", "mappings": {"default": {"default": "o con accento circonflesso e gancio sopra"}}, "category": "Ll"}, {"key": "1ED7", "mappings": {"default": {"default": "o con accento circonflesso e tilde"}}, "category": "Ll"}, {"key": "1ED9", "mappings": {"default": {"default": "o con accento circonflesso e punto sotto"}}, "category": "Ll"}, {"key": "1EDB", "mappings": {"default": {"default": "o con corno e accento acuto"}}, "category": "Ll"}, {"key": "1EDD", "mappings": {"default": {"default": "o con corno e accento grave"}}, "category": "Ll"}, {"key": "1EDF", "mappings": {"default": {"default": "o con corno e gancio sopra"}}, "category": "Ll"}, {"key": "1EE1", "mappings": {"default": {"default": "o con corno e tilde"}}, "category": "Ll"}, {"key": "1EE3", "mappings": {"default": {"default": "o con corno e punto sotto"}}, "category": "Ll"}, {"key": "1EE9", "mappings": {"default": {"default": "u con corno e accento acuto"}}, "category": "Ll"}, {"key": "1EEB", "mappings": {"default": {"default": "u con corno e accento grave"}}, "category": "Ll"}, {"key": "1EED", "mappings": {"default": {"default": "u con corno e gancio sopra"}}, "category": "Ll"}, {"key": "1EEF", "mappings": {"default": {"default": "u con corno e tilde"}}, "category": "Ll"}, {"key": "1EF1", "mappings": {"default": {"default": "u con corno e punto sotto"}}, "category": "Ll"}], "it/symbols/latin-lower-phonetic.min": [{"locale": "it"}, {"key": "00F8", "mappings": {"default": {"default": "o con tratto diagonale"}}, "category": "Ll"}, {"key": "0111", "mappings": {"default": {"default": "d con tratto diagonale"}}, "category": "Ll"}, {"key": "0127", "mappings": {"default": {"default": "h con tratto diagonale"}}, "category": "Ll"}, {"key": "0142", "mappings": {"default": {"default": "l con tratto diagonale"}}, "category": "Ll"}, {"key": "0167", "mappings": {"default": {"default": "t con tratto diagonale"}}, "category": "Ll"}, {"category": "Ll", "key": "0180", "mappings": {"default": {"default": "b con tratto"}}}, {"key": "019B", "mappings": {"default": {"default": "lambda con tratto diagonale"}}, "category": "Ll"}, {"category": "Ll", "key": "01B6", "mappings": {"default": {"default": "latina piccola lettera Z con tratto"}}}, {"category": "Ll", "key": "01BE", "mappings": {"default": {"default": "lettera latina capovolta fermata glottale con tratto"}}}, {"category": "Ll", "key": "01E5", "mappings": {"default": {"default": "g con tratto"}}}, {"category": "Ll", "key": "01FF", "mappings": {"default": {"default": "o con tratto e accento acuto"}}}, {"category": "Ll", "key": "023C", "mappings": {"default": {"default": "c con tratto"}}}, {"category": "Ll", "key": "0247", "mappings": {"default": {"default": "e con tratto"}}}, {"category": "Ll", "key": "0249", "mappings": {"default": {"default": "j con tratto"}}}, {"category": "Ll", "key": "024D", "mappings": {"default": {"default": "r con tratto"}}}, {"category": "Ll", "key": "024F", "mappings": {"default": {"default": "y con tratto"}}}, {"category": "Ll", "key": "025F", "mappings": {"default": {"default": "j senza punto con tratto"}}}, {"category": "Ll", "key": "0268", "mappings": {"default": {"default": "i con tratto"}}}, {"category": "Ll", "key": "0284", "mappings": {"default": {"default": "lettera minuscola latina Dotless J con tratto e gancio"}}}, {"category": "Ll", "key": "02A1", "mappings": {"default": {"default": "lettera latina fermata glottale con tratto"}}}, {"category": "Ll", "key": "02A2", "mappings": {"default": {"default": "lettera latina invertita fermata glottale con tratto"}}}, {"category": "Ll", "key": "1D13", "mappings": {"default": {"default": "o di traverso con tratto"}}}, {"category": "Ll", "key": "1D7C", "mappings": {"default": {"default": "iota con tratto"}}}, {"category": "Ll", "key": "1D7D", "mappings": {"default": {"default": "p con tratto"}}}, {"category": "Ll", "key": "1D7F", "mappings": {"default": {"default": "upsilon con tratto"}}}, {"category": "Ll", "key": "1E9C", "mappings": {"default": {"default": "s lunga con tratto diagonale"}}}, {"category": "Ll", "key": "1E9D", "mappings": {"default": {"default": "s lunga con tratto alto"}}}, {"category": "Ll", "key": "018D", "mappings": {"default": {"default": "delta capovolta"}}}, {"key": "1E9B", "mappings": {"default": {"default": "s lunga con punto sopra"}}, "category": "Ll"}, {"category": "Ll", "key": "1E9F", "mappings": {"default": {"default": "delta"}}}, {"key": "0138", "mappings": {"default": {"default": "kra"}}, "category": "Ll"}, {"category": "Ll", "key": "017F", "mappings": {"default": {"default": "s lunga"}}}, {"category": "Ll", "key": "0183", "mappings": {"default": {"default": "b con tratto in alto"}}}, {"category": "Ll", "key": "0185", "mappings": {"default": {"default": "lettera tono sei"}}}, {"category": "Ll", "key": "0188", "mappings": {"default": {"default": "c con gancio"}}}, {"category": "Ll", "key": "018C", "mappings": {"default": {"default": "d con tratto in alto"}}}, {"key": "0192", "mappings": {"default": {"default": "f con uncino"}}, "category": "Ll"}, {"category": "Ll", "key": "0195", "mappings": {"default": {"default": "lettera Hv"}}}, {"category": "Ll", "key": "0199", "mappings": {"default": {"default": "k con gancio"}}}, {"key": "019A", "mappings": {"default": {"default": "l con barra"}}, "category": "Ll"}, {"category": "Ll", "key": "019E", "mappings": {"default": {"default": "n con lunga gamba destra"}}}, {"category": "Ll", "key": "01A1", "mappings": {"default": {"default": "o con corno"}}}, {"category": "Ll", "key": "01A3", "mappings": {"default": {"default": "lettera Oi"}}}, {"category": "Ll", "key": "01A5", "mappings": {"default": {"default": "latina piccola lettera P con gancio"}}}, {"category": "Ll", "key": "01A8", "mappings": {"default": {"default": "lettera tono due"}}}, {"category": "Ll", "key": "01AA", "mappings": {"default": {"default": "Esh Loop invertita"}}}, {"category": "Ll", "key": "01AB", "mappings": {"default": {"default": "t con gancio palatale"}}}, {"category": "Ll", "key": "01AD", "mappings": {"default": {"default": "t con gancio"}}}, {"category": "Ll", "key": "01B0", "mappings": {"default": {"default": "u con corno"}}}, {"category": "Ll", "key": "01B4", "mappings": {"default": {"default": "y con gancio"}}}, {"category": "Ll", "key": "01B9", "mappings": {"default": {"default": "ezh capovolta"}}}, {"category": "Ll", "key": "01BA", "mappings": {"default": {"default": "ezh con coda"}}}, {"category": "Ll", "key": "01BD", "mappings": {"default": {"default": "lettera tono cinque"}}}, {"category": "Ll", "key": "01BF", "mappings": {"default": {"default": "wynn"}}}, {"category": "Ll", "key": "01C6", "mappings": {"default": {"default": "dz con Car<PERSON>"}}}, {"category": "Ll", "key": "01C9", "mappings": {"default": {"default": "lj"}}}, {"category": "Ll", "key": "01CC", "mappings": {"default": {"default": "nj"}}}, {"category": "Ll", "key": "01E3", "mappings": {"default": {"default": "ae con macron"}}}, {"category": "Ll", "key": "01EF", "mappings": {"default": {"default": "ezh con caron"}}}, {"category": "Ll", "key": "01F3", "mappings": {"default": {"default": "dz"}}}, {"category": "Ll", "key": "021D", "mappings": {"default": {"default": "yogh"}}}, {"category": "Ll", "key": "026E", "mappings": {"default": {"default": "lezh"}}}, {"category": "Ll", "key": "0292", "mappings": {"default": {"default": "ezh"}}}, {"category": "Ll", "key": "0293", "mappings": {"default": {"default": "ezh con ricciolo"}}}, {"category": "Ll", "key": "02A4", "mappings": {"default": {"default": "dezh digrafo"}}}, {"category": "Ll", "key": "01DD", "mappings": {"default": {"default": "e capovolta"}}}, {"category": "Ll", "key": "01FD", "mappings": {"default": {"default": "ae con accento acuto"}}}, {"category": "Ll", "key": "0221", "mappings": {"default": {"default": "d con arricciatura"}}}, {"category": "Ll", "key": "0223", "mappings": {"default": {"default": "ou"}}}, {"category": "Ll", "key": "0225", "mappings": {"default": {"default": "z con gancio"}}}, {"category": "Ll", "key": "0234", "mappings": {"default": {"default": "l con ricciolo"}}}, {"category": "Ll", "key": "0235", "mappings": {"default": {"default": "n con arricciatura"}}}, {"category": "Ll", "key": "0236", "mappings": {"default": {"default": "t con ricciolo"}}}, {"category": "Ll", "key": "0238", "mappings": {"default": {"default": "db digrafo"}}}, {"category": "Ll", "key": "0239", "mappings": {"default": {"default": "qp digrafo"}}}, {"category": "Ll", "key": "023F", "mappings": {"default": {"default": "s con svolazzo alto"}}}, {"category": "Ll", "key": "0240", "mappings": {"default": {"default": "z con svolazzo alto"}}}, {"category": "Ll", "key": "0242", "mappings": {"default": {"default": "fermata glottale"}}}, {"category": "Ll", "key": "024B", "mappings": {"default": {"default": "q con coda a uncino"}}}, {"category": "Ll", "key": "0250", "mappings": {"default": {"default": "a capovolta"}}}, {"category": "Ll", "key": "0251", "mappings": {"default": {"default": "alfa latina"}}}, {"category": "Ll", "key": "0252", "mappings": {"default": {"default": "alfa latina capovolta"}}}, {"category": "Ll", "key": "0253", "mappings": {"default": {"default": "b con gancio"}}}, {"category": "Ll", "key": "0254", "mappings": {"default": {"default": "o aperta"}}}, {"category": "Ll", "key": "0255", "mappings": {"default": {"default": "c con arricciatura"}}}, {"category": "Ll", "key": "0256", "mappings": {"default": {"default": "d con gancio retroflesso"}}}, {"category": "Ll", "key": "0257", "mappings": {"default": {"default": "d con gancio"}}}, {"category": "Ll", "key": "0258", "mappings": {"default": {"default": "e invertita"}}}, {"category": "Ll", "key": "0259", "mappings": {"default": {"default": "schwa"}}}, {"category": "Ll", "key": "025A", "mappings": {"default": {"default": "schwa con gancio"}}}, {"key": "025B", "mappings": {"default": {"default": "e aperta"}}, "category": "Ll"}, {"category": "Ll", "key": "025C", "mappings": {"default": {"default": "e aperta invertita"}}}, {"category": "Ll", "key": "025D", "mappings": {"default": {"default": "e aperta invertita con gancio"}}}, {"category": "Ll", "key": "025E", "mappings": {"default": {"default": "epsilon invertita chiusa"}}}, {"category": "Ll", "key": "0260", "mappings": {"default": {"default": "e con gancio"}}}, {"category": "Ll", "key": "0261", "mappings": {"default": {"default": "script g"}}}, {"category": "Ll", "key": "0263", "mappings": {"default": {"default": "gamma"}}}, {"category": "Ll", "key": "0264", "mappings": {"default": {"default": "baby gamma"}}}, {"category": "Ll", "key": "0265", "mappings": {"default": {"default": "h capovolta"}}}, {"category": "Ll", "key": "0266", "mappings": {"default": {"default": "h con gancio"}}}, {"category": "Ll", "key": "0267", "mappings": {"default": {"default": "heng con gancio"}}}, {"category": "Ll", "key": "0269", "mappings": {"default": {"default": "iota"}}}, {"category": "Ll", "key": "026B", "mappings": {"default": {"default": "l con tilde centrale"}}}, {"category": "Ll", "key": "026C", "mappings": {"default": {"default": "l con cintura"}}}, {"category": "Ll", "key": "026D", "mappings": {"default": {"default": "l con gancio retroflesso"}}}, {"category": "Ll", "key": "026F", "mappings": {"default": {"default": "m capovolta"}}}, {"category": "Ll", "key": "0270", "mappings": {"default": {"default": "m capovolta con gamba lunga"}}}, {"category": "Ll", "key": "0271", "mappings": {"default": {"default": "m con gancio"}}}, {"category": "Ll", "key": "0272", "mappings": {"default": {"default": "n con gancio sinistro"}}}, {"category": "Ll", "key": "0273", "mappings": {"default": {"default": "n con gancio retroflesso"}}}, {"category": "Ll", "key": "0275", "mappings": {"default": {"default": "o barrata"}}}, {"category": "Ll", "key": "0277", "mappings": {"default": {"default": "omega chiusa"}}}, {"category": "Ll", "key": "0278", "mappings": {"default": {"default": "phi"}}}, {"category": "Ll", "key": "0279", "mappings": {"default": {"default": "r capovolta"}}}, {"category": "Ll", "key": "027A", "mappings": {"default": {"default": "r con gamba lunga"}}}, {"category": "Ll", "key": "027B", "mappings": {"default": {"default": "r con gancio"}}}, {"category": "Ll", "key": "027C", "mappings": {"default": {"default": "r con gamba lunga"}}}, {"category": "Ll", "key": "027D", "mappings": {"default": {"default": "r con coda"}}}, {"category": "Ll", "key": "027E", "mappings": {"default": {"default": "r con amo da pesca"}}}, {"category": "Ll", "key": "027F", "mappings": {"default": {"default": "r capovolta con amo"}}}, {"category": "Ll", "key": "0282", "mappings": {"default": {"default": "s con gancio"}}}, {"category": "Ll", "key": "0283", "mappings": {"default": {"default": "esh"}}}, {"category": "Ll", "key": "0285", "mappings": {"default": {"default": "esc invertita"}}}, {"category": "Ll", "key": "0286", "mappings": {"default": {"default": "esh con ricciolo"}}}, {"category": "Ll", "key": "0287", "mappings": {"default": {"default": "t capovolta"}}}, {"category": "Ll", "key": "0288", "mappings": {"default": {"default": "t con gancio retroflesso"}}}, {"category": "Ll", "key": "0289", "mappings": {"default": {"default": "u barrata"}}}, {"category": "Ll", "key": "028A", "mappings": {"default": {"default": "upsilon"}}}, {"category": "Ll", "key": "028B", "mappings": {"default": {"default": "latina piccola lettera V con gancio"}}}, {"category": "Ll", "key": "028C", "mappings": {"default": {"default": "v capovolta"}}}, {"category": "Ll", "key": "028D", "mappings": {"default": {"default": "w capovolta"}}}, {"category": "Ll", "key": "028E", "mappings": {"default": {"default": "y capovolta"}}}, {"category": "Ll", "key": "0290", "mappings": {"default": {"default": "z con gancio retroflesso"}}}, {"category": "Ll", "key": "0291", "mappings": {"default": {"default": "z con arricciatura"}}}, {"category": "Ll", "key": "0295", "mappings": {"default": {"default": "stop glottale invertito"}}}, {"category": "Ll", "key": "0296", "mappings": {"default": {"default": "stop glottale invertito capovolto"}}}, {"category": "Ll", "key": "0297", "mappings": {"default": {"default": "c allungata"}}}, {"category": "Ll", "key": "0298", "mappings": {"default": {"default": "bullseye"}}}, {"category": "Ll", "key": "029A", "mappings": {"default": {"default": "epsilon chiuso"}}}, {"category": "Ll", "key": "029E", "mappings": {"default": {"default": "k capovolta"}}}, {"category": "Ll", "key": "02A0", "mappings": {"default": {"default": "q con gancio"}}}, {"category": "Ll", "key": "02A3", "mappings": {"default": {"default": "digrafo dz"}}}, {"category": "Ll", "key": "02A5", "mappings": {"default": {"default": "digrafo dz con arricciatura"}}}, {"category": "Ll", "key": "02A6", "mappings": {"default": {"default": "digrafo ts"}}}, {"category": "Ll", "key": "02A7", "mappings": {"default": {"default": "digrafo tesh"}}}, {"category": "Ll", "key": "02A8", "mappings": {"default": {"default": "digrafo tc con ricciolo"}}}, {"category": "Ll", "key": "02A9", "mappings": {"default": {"default": "digrafo feng"}}}, {"category": "Ll", "key": "02AA", "mappings": {"default": {"default": "digrafo ls"}}}, {"category": "Ll", "key": "02AB", "mappings": {"default": {"default": "digrafo lz"}}}, {"category": "Ll", "key": "02AC", "mappings": {"default": {"default": "percussivo bilabiale"}}}, {"category": "Ll", "key": "02AD", "mappings": {"default": {"default": "percussivo bidentale"}}}, {"category": "Ll", "key": "02AE", "mappings": {"default": {"default": "h capovolta con amo"}}}, {"category": "Ll", "key": "02AF", "mappings": {"default": {"default": "h capovolta con amo e coda"}}}, {"category": "Ll", "key": "1D02", "mappings": {"default": {"default": "ae capovolta"}}}, {"category": "Ll", "key": "1D08", "mappings": {"default": {"default": "e aperta invertita"}}}, {"category": "Ll", "key": "1D09", "mappings": {"default": {"default": "i capovolta"}}}, {"category": "Ll", "key": "1D11", "mappings": {"default": {"default": "o di traverso"}}}, {"category": "Ll", "key": "1D12", "mappings": {"default": {"default": "o aperta di traverso"}}}, {"category": "Ll", "key": "1D14", "mappings": {"default": {"default": "oe invertita"}}}, {"category": "Ll", "key": "1D16", "mappings": {"default": {"default": "metà alta di o"}}}, {"category": "Ll", "key": "1D17", "mappings": {"default": {"default": "<PERSON>à bassa di o"}}}, {"category": "Ll", "key": "1D1D", "mappings": {"default": {"default": "u di traverso"}}}, {"category": "Ll", "key": "1D1E", "mappings": {"default": {"default": "u con dieresi di traverso"}}}, {"category": "Ll", "key": "1D1F", "mappings": {"default": {"default": "m di traverso"}}}, {"category": "Ll", "key": "1D24", "mappings": {"default": {"default": "spirale laringeo"}}}, {"category": "Ll", "key": "1D25", "mappings": {"default": {"default": "ain"}}}, {"category": "Ll", "key": "1D6B", "mappings": {"default": {"default": "ue"}}}, {"category": "Ll", "key": "1D6C", "mappings": {"default": {"default": "b con tilde centrale"}}}, {"category": "Ll", "key": "1D6D", "mappings": {"default": {"default": "d con tilde centrale"}}}, {"category": "Ll", "key": "1D6E", "mappings": {"default": {"default": "f con tilde centrale"}}}, {"category": "Ll", "key": "1D6F", "mappings": {"default": {"default": "m con tilde centrale"}}}, {"category": "Ll", "key": "1D70", "mappings": {"default": {"default": "n con tilde centrale"}}}, {"category": "Ll", "key": "1D71", "mappings": {"default": {"default": "p con tilde centrale"}}}, {"category": "Ll", "key": "1D72", "mappings": {"default": {"default": "r con tilde centrale"}}}, {"category": "Ll", "key": "1D73", "mappings": {"default": {"default": "r con amo e tilde centrale"}}}, {"category": "Ll", "key": "1D74", "mappings": {"default": {"default": "s con tilde centrale"}}}, {"category": "Ll", "key": "1D75", "mappings": {"default": {"default": "t con tilde centrale"}}}, {"category": "Ll", "key": "1D76", "mappings": {"default": {"default": "z con tilde centrale"}}}, {"category": "Ll", "key": "1D77", "mappings": {"default": {"default": "g capovolta"}}}, {"category": "Ll", "key": "1D79", "mappings": {"default": {"default": "g insulare"}}}, {"category": "Ll", "key": "1D7A", "mappings": {"default": {"default": "th barrata"}}}, {"category": "Ll", "key": "1D80", "mappings": {"default": {"default": "b con gancio palatale"}}}, {"category": "Ll", "key": "1D81", "mappings": {"default": {"default": "d con gancio palatale"}}}, {"category": "Ll", "key": "1D82", "mappings": {"default": {"default": "f con gancio palatale"}}}, {"category": "Ll", "key": "1D83", "mappings": {"default": {"default": "g con gancio palatale"}}}, {"category": "Ll", "key": "1D84", "mappings": {"default": {"default": "k con gancio palatale"}}}, {"category": "Ll", "key": "1D85", "mappings": {"default": {"default": "l con gancio palatale"}}}, {"category": "Ll", "key": "1D86", "mappings": {"default": {"default": "m con gancio palatale"}}}, {"category": "Ll", "key": "1D87", "mappings": {"default": {"default": "n con gancio palatale"}}}, {"category": "Ll", "key": "1D88", "mappings": {"default": {"default": "p con gancio palatale"}}}, {"category": "Ll", "key": "1D89", "mappings": {"default": {"default": "r con gancio palatale"}}}, {"category": "Ll", "key": "1D8A", "mappings": {"default": {"default": "s con gancio palatale"}}}, {"category": "Ll", "key": "1D8B", "mappings": {"default": {"default": "esh con gancio palatale"}}}, {"category": "Ll", "key": "1D8C", "mappings": {"default": {"default": "v con gancio palatale"}}}, {"category": "Ll", "key": "1D8D", "mappings": {"default": {"default": "x con gancio palatale"}}}, {"category": "Ll", "key": "1D8E", "mappings": {"default": {"default": "z con gancio palatale"}}}, {"category": "Ll", "key": "1D8F", "mappings": {"default": {"default": "a con gancio retroflesso"}}}, {"category": "Ll", "key": "1D90", "mappings": {"default": {"default": "alfa con gancio retroflesso"}}}, {"category": "Ll", "key": "1D91", "mappings": {"default": {"default": "d con gancio e coda"}}}, {"category": "Ll", "key": "1D92", "mappings": {"default": {"default": "e con gancio retroflesso"}}}, {"category": "Ll", "key": "1D93", "mappings": {"default": {"default": "e aperta con gancio retroflesso"}}}, {"category": "Ll", "key": "1D94", "mappings": {"default": {"default": "e aperta con gancio retroflesso invertita"}}}, {"category": "Ll", "key": "1D95", "mappings": {"default": {"default": "schwa con gancio retroflesso"}}}, {"category": "Ll", "key": "1D96", "mappings": {"default": {"default": "I con gancio <PERSON>"}}}, {"category": "Ll", "key": "1D97", "mappings": {"default": {"default": "o aperta con gancio retroflesso"}}}, {"category": "Ll", "key": "1D98", "mappings": {"default": {"default": "esh with gancio retroflesso"}}}, {"category": "Ll", "key": "1D99", "mappings": {"default": {"default": "u con gancio retroflesso"}}}, {"category": "Ll", "key": "1D9A", "mappings": {"default": {"default": "ezh con gancio retroflesso"}}}, {"key": "0149", "mappings": {"default": {"default": "n preceduta da apostrofo"}}, "category": "Ll"}, {"key": "014B", "mappings": {"default": {"default": "eng"}}, "category": "Ll"}], "it/symbols/latin-lower-single-accent.min": [{"locale": "it"}, {"key": "00E0", "mappings": {"default": {"default": "a con accento grave"}}, "category": "Ll"}, {"key": "00E1", "mappings": {"default": {"default": "a con accento acuto"}}, "category": "Ll"}, {"key": "00E2", "mappings": {"default": {"default": "a con accento circonflesso"}}, "category": "Ll"}, {"key": "00E3", "mappings": {"default": {"default": "a con tilde"}}, "category": "Ll"}, {"key": "00E4", "mappings": {"default": {"default": "a con dieresi"}}, "category": "Ll"}, {"key": "00E5", "mappings": {"default": {"default": "a con anello sopra"}}, "category": "Ll"}, {"key": "00E7", "mappings": {"default": {"default": "c con cediglia"}}, "category": "Ll"}, {"key": "00E8", "mappings": {"default": {"default": "e con accento grave"}}, "category": "Ll"}, {"key": "00E9", "mappings": {"default": {"default": "e con accento acuto"}}, "category": "Ll"}, {"key": "00EA", "mappings": {"default": {"default": "e con accento circonflesso"}}, "category": "Ll"}, {"key": "00EB", "mappings": {"default": {"default": "e con dieresi"}}, "category": "Ll"}, {"key": "00EC", "mappings": {"default": {"default": "i con accento grave"}}, "category": "Ll"}, {"key": "00ED", "mappings": {"default": {"default": "i con accento acuto"}}, "category": "Ll"}, {"key": "00EE", "mappings": {"default": {"default": "i con accento circonflesso"}}, "category": "Ll"}, {"key": "00EF", "mappings": {"default": {"default": "i con dieresi"}}, "category": "Ll"}, {"key": "00F1", "mappings": {"default": {"default": "n con tilde"}}, "category": "Ll"}, {"key": "00F2", "mappings": {"default": {"default": "o con accento grave"}}, "category": "Ll"}, {"key": "00F3", "mappings": {"default": {"default": "o con accento acuto"}}, "category": "Ll"}, {"key": "00F4", "mappings": {"default": {"default": "o con accento circonflesso"}}, "category": "Ll"}, {"key": "00F5", "mappings": {"default": {"default": "o con tilde"}}, "category": "Ll"}, {"key": "00F6", "mappings": {"default": {"default": "o con dieresi"}}, "category": "Ll"}, {"key": "00F9", "mappings": {"default": {"default": "u con accento grave"}}, "category": "Ll"}, {"key": "00FA", "mappings": {"default": {"default": "u con accento acuto"}}, "category": "Ll"}, {"key": "00FB", "mappings": {"default": {"default": "u con accento circonflesso"}}, "category": "Ll"}, {"key": "00FC", "mappings": {"default": {"default": "u con dieresi"}}, "category": "Ll"}, {"key": "00FD", "mappings": {"default": {"default": "y con accento acuto"}}, "category": "Ll"}, {"key": "00FF", "mappings": {"default": {"default": "y con dieresi"}}, "category": "Ll"}, {"key": "0101", "mappings": {"default": {"default": "a con barra sopra"}}, "category": "Ll"}, {"key": "0103", "mappings": {"default": {"default": "a con segno di vocale corta"}}, "category": "Ll"}, {"key": "0105", "mappings": {"default": {"default": "a con ogonek"}}, "category": "Ll"}, {"key": "0107", "mappings": {"default": {"default": "c con accento acuto"}}, "category": "Ll"}, {"key": "0109", "mappings": {"default": {"default": "c con accento circonflesso"}}, "category": "Ll"}, {"key": "010B", "mappings": {"default": {"default": "c con un punto sopra"}}, "category": "Ll"}, {"key": "010D", "mappings": {"default": {"default": "c con caron"}}, "category": "Ll"}, {"key": "010F", "mappings": {"default": {"default": "d con caron"}}, "category": "Ll"}, {"key": "0113", "mappings": {"default": {"default": "e con barra sopra"}}, "category": "Ll"}, {"category": "Ll", "key": "0115", "mappings": {"default": {"default": "piccola lettera latina E con Breve"}}}, {"key": "0117", "mappings": {"default": {"default": "e con punto sopra"}}, "category": "Ll"}, {"key": "0119", "mappings": {"default": {"default": "e con ogonek"}}, "category": "Ll"}, {"key": "011B", "mappings": {"default": {"default": "e con caron"}}, "category": "Ll"}, {"key": "011D", "mappings": {"default": {"default": "g con accento circonflesso"}}, "category": "Ll"}, {"key": "011F", "mappings": {"default": {"default": "g con segno di vocale corta"}}, "category": "Ll"}, {"key": "0121", "mappings": {"default": {"default": "g con punto sopra"}}, "category": "Ll"}, {"category": "Ll", "key": "0123", "mappings": {"default": {"default": "latina piccola lettera G con Cedilla"}}}, {"key": "0125", "mappings": {"default": {"default": "h con accento circonflesso"}}, "category": "Ll"}, {"key": "0129", "mappings": {"default": {"default": "i con tilde"}}, "category": "Ll"}, {"key": "012B", "mappings": {"default": {"default": "i con barra sopra"}}, "category": "Ll"}, {"category": "Ll", "key": "012D", "mappings": {"default": {"default": "piccola lettera latina I con breve"}}}, {"key": "012F", "mappings": {"default": {"default": "i con ogonek"}}, "category": "Ll"}, {"key": "0131", "mappings": {"default": {"default": "i senza puntino"}}, "category": "Ll"}, {"key": "0135", "mappings": {"default": {"default": "gei con accento circonflesso"}}, "category": "Ll"}, {"key": "0137", "mappings": {"default": {"default": "k con cediglia"}}, "category": "Ll"}, {"key": "013A", "mappings": {"default": {"default": "l con accento"}}, "category": "Ll"}, {"key": "013C", "mappings": {"default": {"default": "l con cediglia"}}, "category": "Ll"}, {"key": "013E", "mappings": {"default": {"default": "l con caron"}}, "category": "Ll"}, {"key": "0140", "mappings": {"default": {"default": "l con punto nel mezzo"}}, "category": "Ll"}, {"key": "0144", "mappings": {"default": {"default": "n con accento acuto"}}, "category": "Ll"}, {"key": "0146", "mappings": {"default": {"default": "n con cediglia"}}, "category": "Ll"}, {"key": "0148", "mappings": {"default": {"default": "n con caron"}}, "category": "Ll"}, {"key": "014D", "mappings": {"default": {"default": "o con barra sopra"}}, "category": "Ll"}, {"category": "Ll", "key": "014F", "mappings": {"default": {"default": "lettera minuscola latina O con Breve"}}}, {"key": "0151", "mappings": {"default": {"default": "o con doppio accento acuto"}}, "category": "Ll"}, {"key": "0155", "mappings": {"default": {"default": "r con accento acuto"}}, "category": "Ll"}, {"key": "0157", "mappings": {"default": {"default": "r con cediglia"}}, "category": "Ll"}, {"key": "0159", "mappings": {"default": {"default": "r con caron"}}, "category": "Ll"}, {"key": "015B", "mappings": {"default": {"default": "s con accento acuto"}}, "category": "Ll"}, {"key": "015D", "mappings": {"default": {"default": "s con accento circonflesso"}}, "category": "Ll"}, {"key": "015F", "mappings": {"default": {"default": "s con cediglia"}}, "category": "Ll"}, {"key": "0161", "mappings": {"default": {"default": "s con caron"}}, "category": "Ll"}, {"key": "0163", "mappings": {"default": {"default": "t con cediglia"}}, "category": "Ll"}, {"key": "0165", "mappings": {"default": {"default": "t con caron"}}, "category": "Ll"}, {"key": "0169", "mappings": {"default": {"default": "u con tilde"}}, "category": "Ll"}, {"key": "016B", "mappings": {"default": {"default": "u con barra sopra"}}, "category": "Ll"}, {"key": "016D", "mappings": {"default": {"default": "u con segno di vocale corta"}}, "category": "Ll"}, {"key": "016F", "mappings": {"default": {"default": "u con anello sopra"}}, "category": "Ll"}, {"key": "0171", "mappings": {"default": {"default": "u con doppio accento acuto"}}, "category": "Ll"}, {"key": "0173", "mappings": {"default": {"default": "u con ogonek"}}, "category": "Ll"}, {"key": "0175", "mappings": {"default": {"default": "w con accento circonflesso"}}, "category": "Ll"}, {"key": "0177", "mappings": {"default": {"default": "y con accento circonflesso"}}, "category": "Ll"}, {"key": "017A", "mappings": {"default": {"default": "z con accento acuto"}}, "category": "Ll"}, {"key": "017C", "mappings": {"default": {"default": "z con punto sopra"}}, "category": "Ll"}, {"key": "017E", "mappings": {"default": {"default": "z con caron"}}, "category": "Ll"}, {"category": "Ll", "key": "01CE", "mappings": {"default": {"default": "a con caron"}}}, {"category": "Ll", "key": "01D0", "mappings": {"default": {"default": "i con caron"}}}, {"category": "Ll", "key": "01D2", "mappings": {"default": {"default": "o con caron"}}}, {"category": "Ll", "key": "01D4", "mappings": {"default": {"default": "u con caron"}}}, {"category": "Ll", "key": "01E7", "mappings": {"default": {"default": "g con caron"}}}, {"category": "Ll", "key": "01E9", "mappings": {"default": {"default": "k con caron"}}}, {"category": "Ll", "key": "01EB", "mappings": {"default": {"default": "o con uncino"}}}, {"category": "Ll", "key": "01F0", "mappings": {"default": {"default": "j con caron"}}}, {"key": "01F5", "mappings": {"default": {"default": "g con accento acuto"}}, "category": "Ll"}, {"category": "Ll", "key": "01F9", "mappings": {"default": {"default": "n con accento grave"}}}, {"category": "Ll", "key": "0201", "mappings": {"default": {"default": "a con doppio accento grave"}}}, {"category": "Ll", "key": "0203", "mappings": {"default": {"default": "a con accento breve inverso"}}}, {"category": "Ll", "key": "0205", "mappings": {"default": {"default": "e con doppio accento grave"}}}, {"category": "Ll", "key": "0207", "mappings": {"default": {"default": "e con accento breve inverso"}}}, {"category": "Ll", "key": "0209", "mappings": {"default": {"default": "I con doppio accento grave"}}}, {"category": "Ll", "key": "020B", "mappings": {"default": {"default": "i con accento breve inverso"}}}, {"category": "Ll", "key": "020D", "mappings": {"default": {"default": "o con doppio accento grave"}}}, {"category": "Ll", "key": "020F", "mappings": {"default": {"default": "o con accento breve inverso"}}}, {"category": "Ll", "key": "0211", "mappings": {"default": {"default": "r con doppio accento grave"}}}, {"category": "Ll", "key": "0213", "mappings": {"default": {"default": "r con accento breve"}}}, {"category": "Ll", "key": "0215", "mappings": {"default": {"default": "u con doppio accento grave"}}}, {"category": "Ll", "key": "0217", "mappings": {"default": {"default": "u con accento breve inverso"}}}, {"category": "Ll", "key": "0219", "mappings": {"default": {"default": "s con virgola in basso"}}}, {"category": "Ll", "key": "021B", "mappings": {"default": {"default": "t con virgola in basso"}}}, {"category": "Ll", "key": "021F", "mappings": {"default": {"default": "h con caron"}}}, {"category": "Ll", "key": "0227", "mappings": {"default": {"default": "a con punto in alto"}}}, {"category": "Ll", "key": "0229", "mappings": {"default": {"default": "e con cediglia"}}}, {"category": "Ll", "key": "022F", "mappings": {"default": {"default": "o con punto in alto"}}}, {"category": "Ll", "key": "0233", "mappings": {"default": {"default": "y con macron"}}}, {"category": "Ll", "key": "0237", "mappings": {"default": {"default": "j senza punto"}}}, {"key": "1E01", "mappings": {"default": {"default": "a con anello sotto"}}, "category": "Ll"}, {"key": "1E03", "mappings": {"default": {"default": "b con punto sopra"}}, "category": "Ll"}, {"key": "1E05", "mappings": {"default": {"default": "b con punto sotto"}}, "category": "Ll"}, {"key": "1E07", "mappings": {"default": {"default": "b con linea sotto"}}, "category": "Ll"}, {"key": "1E0B", "mappings": {"default": {"default": "d con punto sopra"}}, "category": "Ll"}, {"key": "1E0D", "mappings": {"default": {"default": "d con punto sotto"}}, "category": "Ll"}, {"key": "1E0F", "mappings": {"default": {"default": "d con linea sotto"}}, "category": "Ll"}, {"key": "1E11", "mappings": {"default": {"default": "d con cediglia"}}, "category": "Ll"}, {"key": "1E13", "mappings": {"default": {"default": "d con accento circonflesso sotto"}}, "category": "Ll"}, {"key": "1E19", "mappings": {"default": {"default": "e con accento circonflesso sotto"}}, "category": "Ll"}, {"key": "1E1B", "mappings": {"default": {"default": "e con tilde sotto"}}, "category": "Ll"}, {"key": "1E1F", "mappings": {"default": {"default": "f con punto sopra"}}, "category": "Ll"}, {"key": "1E21", "mappings": {"default": {"default": "g con barra sopra"}}, "category": "Ll"}, {"key": "1E23", "mappings": {"default": {"default": "h con punto sopra"}}, "category": "Ll"}, {"key": "1E25", "mappings": {"default": {"default": "h con punto sotto"}}, "category": "Ll"}, {"key": "1E27", "mappings": {"default": {"default": "h con dieresi"}}, "category": "Ll"}, {"key": "1E29", "mappings": {"default": {"default": "h con cediglia"}}, "category": "Ll"}, {"key": "1E2B", "mappings": {"default": {"default": "h con segno di vocale corta sotto"}}, "category": "Ll"}, {"key": "1E2D", "mappings": {"default": {"default": "i con tilde sotto"}}, "category": "Ll"}, {"key": "1E31", "mappings": {"default": {"default": "k con accento acuto"}}, "category": "Ll"}, {"key": "1E33", "mappings": {"default": {"default": "k con punto sotto"}}, "category": "Ll"}, {"key": "1E35", "mappings": {"default": {"default": "k con linea sotto"}}, "category": "Ll"}, {"key": "1E37", "mappings": {"default": {"default": "l con punto sotto"}}, "category": "Ll"}, {"key": "1E3B", "mappings": {"default": {"default": "l con linea sotto"}}, "category": "Ll"}, {"key": "1E3D", "mappings": {"default": {"default": "l con accento circonflesso sotto"}}, "category": "Ll"}, {"key": "1E3F", "mappings": {"default": {"default": "m con accento acuto"}}, "category": "Ll"}, {"key": "1E41", "mappings": {"default": {"default": "m con punto sopra"}}, "category": "Ll"}, {"key": "1E43", "mappings": {"default": {"default": "m con punto sotto"}}, "category": "Ll"}, {"key": "1E45", "mappings": {"default": {"default": "n con punto sopra"}}, "category": "Ll"}, {"key": "1E47", "mappings": {"default": {"default": "n con punto sotto"}}, "category": "Ll"}, {"key": "1E49", "mappings": {"default": {"default": "n con linea sotto"}}, "category": "Ll"}, {"key": "1E4B", "mappings": {"default": {"default": "n con accento circonflesso sotto"}}, "category": "Ll"}, {"key": "1E55", "mappings": {"default": {"default": "p con accento acuto"}}, "category": "Ll"}, {"key": "1E57", "mappings": {"default": {"default": "p con punto sopra"}}, "category": "Ll"}, {"key": "1E59", "mappings": {"default": {"default": "r con punto sopra"}}, "category": "Ll"}, {"key": "1E5B", "mappings": {"default": {"default": "r con punto sotto"}}, "category": "Ll"}, {"key": "1E5F", "mappings": {"default": {"default": "r con linea sotto"}}, "category": "Ll"}, {"key": "1E61", "mappings": {"default": {"default": "s con punto sopra"}}, "category": "Ll"}, {"key": "1E63", "mappings": {"default": {"default": "s con punto sotto"}}, "category": "Ll"}, {"key": "1E6B", "mappings": {"default": {"default": "t con punto sopra"}}, "category": "Ll"}, {"key": "1E6D", "mappings": {"default": {"default": "t con punto sotto"}}, "category": "Ll"}, {"key": "1E6F", "mappings": {"default": {"default": "t con linea sotto"}}, "category": "Ll"}, {"key": "1E71", "mappings": {"default": {"default": "t con accento circonflesso sotto"}}, "category": "Ll"}, {"key": "1E73", "mappings": {"default": {"default": "u con dieresi sotto"}}, "category": "Ll"}, {"key": "1E75", "mappings": {"default": {"default": "u con tilde sotto"}}, "category": "Ll"}, {"key": "1E77", "mappings": {"default": {"default": "u con accento circonflesso sotto"}}, "category": "Ll"}, {"key": "1E7D", "mappings": {"default": {"default": "v con tilde"}}, "category": "Ll"}, {"key": "1E7F", "mappings": {"default": {"default": "v con punto sotto"}}, "category": "Ll"}, {"key": "1E81", "mappings": {"default": {"default": "w con accento grave"}}, "category": "Ll"}, {"key": "1E83", "mappings": {"default": {"default": "w con accento acuto"}}, "category": "Ll"}, {"key": "1E85", "mappings": {"default": {"default": "w con dieresi"}}, "category": "Ll"}, {"key": "1E87", "mappings": {"default": {"default": "w con punto sopra"}}, "category": "Ll"}, {"key": "1E89", "mappings": {"default": {"default": "w con punto sotto"}}, "category": "Ll"}, {"key": "1E8B", "mappings": {"default": {"default": "x con punto sopra"}}, "category": "Ll"}, {"key": "1E8D", "mappings": {"default": {"default": "x con dieresi"}}, "category": "Ll"}, {"key": "1E8F", "mappings": {"default": {"default": "y con punto sopra"}}, "category": "Ll"}, {"key": "1E91", "mappings": {"default": {"default": "z con accento circonflesso"}}, "category": "Ll"}, {"key": "1E93", "mappings": {"default": {"default": "z con punto sotto"}}, "category": "Ll"}, {"key": "1E95", "mappings": {"default": {"default": "z con linea sotto"}}, "category": "Ll"}, {"key": "1E96", "mappings": {"default": {"default": "h con linea sotto"}}, "category": "Ll"}, {"key": "1E97", "mappings": {"default": {"default": "t con dieresi"}}, "category": "Ll"}, {"key": "1E98", "mappings": {"default": {"default": "w con anello sopra"}}, "category": "Ll"}, {"key": "1E99", "mappings": {"default": {"default": "y con anello sopra"}}, "category": "Ll"}, {"key": "1E9A", "mappings": {"default": {"default": "a con metà anello destro"}}, "category": "Ll"}, {"key": "1EA1", "mappings": {"default": {"default": "a con punto sotto"}}, "category": "Ll"}, {"key": "1EA3", "mappings": {"default": {"default": "a con gancio sopra"}}, "category": "Ll"}, {"key": "1EB9", "mappings": {"default": {"default": "e con punto sotto"}}, "category": "Ll"}, {"key": "1EBB", "mappings": {"default": {"default": "e con gancio sopra"}}, "category": "Ll"}, {"key": "1EBD", "mappings": {"default": {"default": "e con tilde"}}, "category": "Ll"}, {"key": "1EC9", "mappings": {"default": {"default": "i con gancio sopra"}}, "category": "Ll"}, {"key": "1ECB", "mappings": {"default": {"default": "i con punto sotto"}}, "category": "Ll"}, {"key": "1ECD", "mappings": {"default": {"default": "o con punto sotto"}}, "category": "Ll"}, {"key": "1ECF", "mappings": {"default": {"default": "o con gancio sopra"}}, "category": "Ll"}, {"key": "1EE5", "mappings": {"default": {"default": "u con punto sotto"}}, "category": "Ll"}, {"key": "1EE7", "mappings": {"default": {"default": "u con gancio sopra"}}, "category": "Ll"}, {"key": "1EF3", "mappings": {"default": {"default": "y con accento grave"}}, "category": "Ll"}, {"key": "1EF5", "mappings": {"default": {"default": "y con punto sotto"}}, "category": "Ll"}, {"key": "1EF7", "mappings": {"default": {"default": "y con gancio sopra"}}, "category": "Ll"}, {"key": "1EF9", "mappings": {"default": {"default": "y con tilde"}}, "category": "Ll"}], "it/symbols/latin-rest.min": [{"locale": "it"}, {"key": "210E", "mappings": {"default": {"default": "costante di planck"}}, "category": "Ll"}, {"category": "Mn", "key": "0363", "mappings": {"default": {"default": "combinando la piccola A"}}}, {"category": "Mn", "key": "0364", "mappings": {"default": {"default": "combinando la piccola E"}}}, {"category": "Mn", "key": "0365", "mappings": {"default": {"default": "combinando la piccola I"}}}, {"category": "Mn", "key": "0366", "mappings": {"default": {"default": "combinare la piccola O"}}}, {"category": "Mn", "key": "0367", "mappings": {"default": {"default": "combinazione di caratteri latini piccoli U"}}}, {"category": "Mn", "key": "0368", "mappings": {"default": {"default": "combinare la piccola C"}}}, {"category": "Mn", "key": "0369", "mappings": {"default": {"default": "combinare la lettera minuscola latina D"}}}, {"category": "Mn", "key": "036A", "mappings": {"default": {"default": "combinare la lettera minuscola latina H"}}}, {"category": "Mn", "key": "036B", "mappings": {"default": {"default": "combinare la lettera minuscola latina M"}}}, {"category": "Mn", "key": "036C", "mappings": {"default": {"default": "combinare la lettera minuscola latina R"}}}, {"category": "Mn", "key": "036D", "mappings": {"default": {"default": "combinando la piccola T"}}}, {"category": "Mn", "key": "036E", "mappings": {"default": {"default": "combinare la lettera minuscola latina V"}}}, {"category": "Mn", "key": "036F", "mappings": {"default": {"default": "combinare la piccola X"}}}, {"category": "Lm", "key": "1D62", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera I"}}}, {"category": "Lm", "key": "1D63", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera R"}}}, {"category": "Lm", "key": "1D64", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera U"}}}, {"category": "Lm", "key": "1D65", "mappings": {"default": {"default": "sottoscrizione latina V lettera minuscola"}}}, {"category": "Mn", "key": "1DCA", "mappings": {"default": {"default": "combinando la piccola R sotto"}}}, {"category": "Mn", "key": "1DD3", "mappings": {"default": {"default": "combinare la lettera minuscola latina appiattita Apri sopra"}}}, {"category": "Mn", "key": "1DD4", "mappings": {"default": {"default": "combina la piccola Ae"}}}, {"category": "Mn", "key": "1DD5", "mappings": {"default": {"default": "combinare la piccola Ao"}}}, {"category": "Mn", "key": "1DD6", "mappings": {"default": {"default": "combinare la lettera minuscola latina Av"}}}, {"category": "Mn", "key": "1DD7", "mappings": {"default": {"default": "combinando la piccola C Cedilla"}}}, {"category": "Mn", "key": "1DD8", "mappings": {"default": {"default": "combinando la lettera minuscola latina insulare D"}}}, {"category": "Mn", "key": "1DD9", "mappings": {"default": {"default": "combinazione di caratteri latini piccoli Eth"}}}, {"category": "Mn", "key": "1DDA", "mappings": {"default": {"default": "combinando la piccola G"}}}, {"category": "Mn", "key": "1DDB", "mappings": {"default": {"default": "combinando la Small Capital G"}}}, {"category": "Mn", "key": "1DDC", "mappings": {"default": {"default": "combinare la piccola K"}}}, {"category": "Mn", "key": "1DDD", "mappings": {"default": {"default": "combinare la piccola L"}}}, {"category": "Mn", "key": "1DDE", "mappings": {"default": {"default": "combinando la Small Capital L"}}}, {"category": "Mn", "key": "1DDF", "mappings": {"default": {"default": "combinare la Small Capital M"}}}, {"category": "Mn", "key": "1DE0", "mappings": {"default": {"default": "combinare la piccola N"}}}, {"category": "Mn", "key": "1DE1", "mappings": {"default": {"default": "combinare la Small Capital N"}}}, {"category": "Mn", "key": "1DE2", "mappings": {"default": {"default": "combinando la Small Capital R"}}}, {"category": "Mn", "key": "1DE3", "mappings": {"default": {"default": "combinazione di caratteri latini R Rotunda"}}}, {"category": "Mn", "key": "1DE4", "mappings": {"default": {"default": "combinare la piccola S"}}}, {"category": "Mn", "key": "1DE5", "mappings": {"default": {"default": "combinando la piccola lunga S"}}}, {"category": "Mn", "key": "1DE6", "mappings": {"default": {"default": "combinando la piccola Z"}}}, {"category": "Lm", "key": "2071", "mappings": {"default": {"default": "apice latina piccola lettera I"}}}, {"category": "Lm", "key": "207F", "mappings": {"default": {"default": "apice latina Piccola lettera N"}}}, {"category": "Lm", "key": "2090", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera A"}}}, {"category": "Lm", "key": "2091", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera E"}}}, {"category": "Lm", "key": "2092", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera O"}}}, {"category": "Lm", "key": "2093", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera X"}}}, {"category": "Lm", "key": "2094", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera Schwa"}}}, {"category": "Lm", "key": "2095", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera H"}}}, {"category": "Lm", "key": "2096", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera K"}}}, {"category": "Lm", "key": "2097", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera L"}}}, {"category": "Lm", "key": "2098", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera M"}}}, {"category": "Lm", "key": "2099", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera N"}}}, {"category": "Lm", "key": "209A", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera P"}}}, {"category": "Lm", "key": "209B", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera S"}}}, {"category": "Lm", "key": "209C", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera T"}}}, {"category": "Lm", "key": "2C7C", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera J"}}}, {"category": "So", "key": "1F12A", "mappings": {"default": {"default": "lettera maiuscola latina a caratteri cubitali con guscio di tartaruga s"}}}, {"category": "So", "key": "1F12B", "mappings": {"default": {"default": "registrazione su disco singolo"}}}, {"category": "So", "key": "1F12C", "mappings": {"default": {"default": "registrazione del disco"}}}, {"category": "So", "key": "1F18A", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>o"}}}], "it/symbols/latin-upper-double-accent.min": [{"locale": "it"}, {"category": "<PERSON>", "key": "01D5", "mappings": {"default": {"default": "Latin Capital Lettera U con Diaeresis e Macron"}}}, {"category": "<PERSON>", "key": "01D7", "mappings": {"default": {"default": "Capitale latina Lettera U con diaeresi e acuta"}}}, {"category": "<PERSON>", "key": "01D9", "mappings": {"default": {"default": "Latin Capital Lettera U con Diaeresis e Caron"}}}, {"category": "<PERSON>", "key": "01DB", "mappings": {"default": {"default": "Capitale latina Lettera U con diaeresi e tomba"}}}, {"category": "<PERSON>", "key": "01DE", "mappings": {"default": {"default": "Lettera maiuscola latina a con Diaeresis e Macron"}}}, {"category": "<PERSON>", "key": "01E0", "mappings": {"default": {"default": "Lettera maiuscola latina a con Punto Sopra e Macron"}}}, {"category": "<PERSON>", "key": "01EC", "mappings": {"default": {"default": "Lettera maiuscola latina O con Ogonek e Macron"}}}, {"category": "<PERSON>", "key": "01FA", "mappings": {"default": {"default": "Lettera maiuscola latina a con anello sopra e acuto"}}}, {"category": "<PERSON>", "key": "022A", "mappings": {"default": {"default": "Capitale latina Lettera O con Diaeresis e Macron"}}}, {"category": "<PERSON>", "key": "022C", "mappings": {"default": {"default": "Capitale latina Lettera O con Tilde e Macron"}}}, {"category": "<PERSON>", "key": "0230", "mappings": {"default": {"default": "Capitale latina Lettera O con Punto Sopra e Macron"}}}, {"key": "1E08", "mappings": {"default": {"default": "c maiuscola con cediglia and acute"}}, "category": "<PERSON>"}, {"key": "1E14", "mappings": {"default": {"default": "e maiuscola con barra sopra e accento grave"}}, "category": "<PERSON>"}, {"key": "1E16", "mappings": {"default": {"default": "e maiuscola con barra sopra e accento acuto"}}, "category": "<PERSON>"}, {"key": "1E1C", "mappings": {"default": {"default": "e maiuscola con cediglia e con segno di vocale corta"}}, "category": "<PERSON>"}, {"key": "1E2E", "mappings": {"default": {"default": "i maiuscola con dieresi ed accento acuto"}}, "category": "<PERSON>"}, {"key": "1E38", "mappings": {"default": {"default": "l maiuscola con punto sotto e con barra sopra"}}, "category": "<PERSON>"}, {"key": "1E4C", "mappings": {"default": {"default": "o maiuscola con tilde ed accento acuto"}}, "category": "<PERSON>"}, {"key": "1E4E", "mappings": {"default": {"default": "o maiuscola con tilde e dieresi"}}, "category": "<PERSON>"}, {"key": "1E50", "mappings": {"default": {"default": "o maiuscola con barra sopra ed accento grave"}}, "category": "<PERSON>"}, {"key": "1E52", "mappings": {"default": {"default": "o maiuscola con barra sopra ed accento acuto"}}, "category": "<PERSON>"}, {"key": "1E5C", "mappings": {"default": {"default": "r ma<PERSON><PERSON> con punto sotto e con barra sopra"}}, "category": "<PERSON>"}, {"key": "1E64", "mappings": {"default": {"default": "s maiuscola con accento acuto e punto sopra"}}, "category": "<PERSON>"}, {"key": "1E66", "mappings": {"default": {"default": "s maiuscola con caron e punto sopra"}}, "category": "<PERSON>"}, {"key": "1E68", "mappings": {"default": {"default": "s maiuscola con punto sotto e punto sopra"}}, "category": "<PERSON>"}, {"key": "1E78", "mappings": {"default": {"default": "u maiuscola con tilde ed accento acuto"}}, "category": "<PERSON>"}, {"key": "1E7A", "mappings": {"default": {"default": "u maiuscola con barra sopra e dieresi"}}, "category": "<PERSON>"}, {"key": "1EA4", "mappings": {"default": {"default": "a maiuscola con accento circonflesso ed accento acuto"}}, "category": "<PERSON>"}, {"key": "1EA6", "mappings": {"default": {"default": "a maiuscola con accento circonflesso ed accento grave"}}, "category": "<PERSON>"}, {"key": "1EA8", "mappings": {"default": {"default": "a maiuscola con accento circonflesso e gancio sopra"}}, "category": "<PERSON>"}, {"key": "1EAA", "mappings": {"default": {"default": "a maiuscola con accento circonflesso e tilde"}}, "category": "<PERSON>"}, {"key": "1EAC", "mappings": {"default": {"default": "a maiuscola con accento circonflesso e punto sotto"}}, "category": "<PERSON>"}, {"key": "1EAE", "mappings": {"default": {"default": "a maiuscola con segno di vocale corta ed accento acuto"}}, "category": "<PERSON>"}, {"key": "1EB0", "mappings": {"default": {"default": "a maiuscola con segno di vocale corta ed accento grave"}}, "category": "<PERSON>"}, {"key": "1EB2", "mappings": {"default": {"default": "a maiuscola con segno di vocale corta e gancio sopra"}}, "category": "<PERSON>"}, {"key": "1EB4", "mappings": {"default": {"default": "a maiuscola con segno di vocale corta e tilde"}}, "category": "<PERSON>"}, {"key": "1EB6", "mappings": {"default": {"default": "a maiuscola con segno di vocale corta e punto sotto"}}, "category": "<PERSON>"}, {"key": "1EBE", "mappings": {"default": {"default": "e maiuscola con accento circonflesso ed acuto"}}, "category": "<PERSON>"}, {"key": "1EC0", "mappings": {"default": {"default": "e maiuscola con accento circonflesso e grave"}}, "category": "<PERSON>"}, {"key": "1EC2", "mappings": {"default": {"default": "e maiuscola con accento circonflesso e gancio sopra"}}, "category": "<PERSON>"}, {"key": "1EC4", "mappings": {"default": {"default": "e maiuscola con accento circonflesso e tilde"}}, "category": "<PERSON>"}, {"key": "1EC6", "mappings": {"default": {"default": "e maiuscola con accento circonflesso e punto sotto"}}, "category": "<PERSON>"}, {"key": "1ED0", "mappings": {"default": {"default": "o maiuscola con accento circonflesso e accento acuto"}}, "category": "<PERSON>"}, {"key": "1ED2", "mappings": {"default": {"default": "o maiuscola con accento circonflesso e accento grave"}}, "category": "<PERSON>"}, {"key": "1ED4", "mappings": {"default": {"default": "o maiuscola con accento circonflesso e gancio sopra"}}, "category": "<PERSON>"}, {"key": "1ED6", "mappings": {"default": {"default": "o maiuscola con accento circonflesso e tilde"}}, "category": "<PERSON>"}, {"key": "1ED8", "mappings": {"default": {"default": "o maiuscola con accento circonflesso e punto sotto"}}, "category": "<PERSON>"}, {"key": "1EDA", "mappings": {"default": {"default": "o maiuscola con corno e accento acuto"}}, "category": "<PERSON>"}, {"key": "1EDC", "mappings": {"default": {"default": "o maiuscola con corno e accento grave"}}, "category": "<PERSON>"}, {"key": "1EDE", "mappings": {"default": {"default": "o maiuscola con corno e gancio sopra"}}, "category": "<PERSON>"}, {"key": "1EE0", "mappings": {"default": {"default": "o maiuscola con corno e tilde"}}, "category": "<PERSON>"}, {"key": "1EE2", "mappings": {"default": {"default": "o maiuscola con corno e punto sotto"}}, "category": "<PERSON>"}, {"key": "1EE8", "mappings": {"default": {"default": "u maiuscola con corno e accento acuto"}}, "category": "<PERSON>"}, {"key": "1EEA", "mappings": {"default": {"default": "u maiuscola con corno e accento grave"}}, "category": "<PERSON>"}, {"key": "1EEC", "mappings": {"default": {"default": "u maiuscola con corno e gancio sopra"}}, "category": "<PERSON>"}, {"key": "1EEE", "mappings": {"default": {"default": "u maiuscola con corno e tilde"}}, "category": "<PERSON>"}, {"key": "1EF0", "mappings": {"default": {"default": "u maiuscola con corno e punto sotto"}}, "category": "<PERSON>"}], "it/symbols/latin-upper-single-accent.min": [{"locale": "it"}, {"key": "00C0", "mappings": {"default": {"default": "a maiuscola con accento grave"}}, "category": "<PERSON>"}, {"key": "00C1", "mappings": {"default": {"default": "a maiuscola con accento acuto"}}, "category": "<PERSON>"}, {"key": "00C2", "mappings": {"default": {"default": "a maiuscola con accento circonflesso"}}, "category": "<PERSON>"}, {"key": "00C3", "mappings": {"default": {"default": "a maiuscola con tilde"}}, "category": "<PERSON>"}, {"key": "00C4", "mappings": {"default": {"default": "a maiuscola con dieresi"}}, "category": "<PERSON>"}, {"key": "00C5", "mappings": {"default": {"default": "a maiuscola con accento un anellino sopra"}}, "category": "<PERSON>"}, {"key": "00C7", "mappings": {"default": {"default": "c maiuscola con cediglia"}}, "category": "<PERSON>"}, {"key": "00C8", "mappings": {"default": {"default": "e maiuscola con accento grave"}}, "category": "<PERSON>"}, {"key": "00C9", "mappings": {"default": {"default": "e maiuscola con accento acuto"}}, "category": "<PERSON>"}, {"key": "00CA", "mappings": {"default": {"default": "e maiuscola con accento circonflesso"}}, "category": "<PERSON>"}, {"key": "00CB", "mappings": {"default": {"default": "e maiuscola con dieresi"}}, "category": "<PERSON>"}, {"key": "00CC", "mappings": {"default": {"default": "i maiuscola con accento grave"}}, "category": "<PERSON>"}, {"key": "00CD", "mappings": {"default": {"default": "i maiuscola con accento acuto"}}, "category": "<PERSON>"}, {"key": "00CE", "mappings": {"default": {"default": "i maiuscola con accento circonflesso"}}, "category": "<PERSON>"}, {"key": "00CF", "mappings": {"default": {"default": "i maiuscola con diaeresis"}}, "category": "<PERSON>"}, {"key": "00D1", "mappings": {"default": {"default": "n maiuscola con tilde"}}, "category": "<PERSON>"}, {"key": "00D2", "mappings": {"default": {"default": "o maiuscola con accento grave"}}, "category": "<PERSON>"}, {"key": "00D3", "mappings": {"default": {"default": "o maiuscola con accento acuto"}}, "category": "<PERSON>"}, {"key": "00D4", "mappings": {"default": {"default": "o maiuscola con accento circonflesso"}}, "category": "<PERSON>"}, {"key": "00D5", "mappings": {"default": {"default": "o maiuscola con tilde"}}, "category": "<PERSON>"}, {"key": "00D6", "mappings": {"default": {"default": "o maiuscola con dieresi"}}, "category": "<PERSON>"}, {"key": "00D9", "mappings": {"default": {"default": "u maiuscola con accento grave"}}, "category": "<PERSON>"}, {"key": "00DA", "mappings": {"default": {"default": "u maiuscola con accento acuto"}}, "category": "<PERSON>"}, {"key": "00DB", "mappings": {"default": {"default": "u maiuscola con accento circonflesso"}}, "category": "<PERSON>"}, {"key": "00DC", "mappings": {"default": {"default": "u maiuscola con dieresi"}}, "category": "<PERSON>"}, {"key": "00DD", "mappings": {"default": {"default": "y maiuscola con accento acuto"}}, "category": "<PERSON>"}, {"key": "0100", "mappings": {"default": {"default": "a maiuscolo con barra sopra"}}, "category": "<PERSON>"}, {"key": "0102", "mappings": {"default": {"default": "a maiuscola con segno di vocale corta"}}, "category": "<PERSON>"}, {"key": "0104", "mappings": {"default": {"default": "a maiuscola con ogonek"}}, "category": "<PERSON>"}, {"key": "0106", "mappings": {"default": {"default": "c ma<PERSON><PERSON> con accento acuto"}}, "category": "<PERSON>"}, {"key": "0108", "mappings": {"default": {"default": "c ma<PERSON><PERSON> con accento circonflesso"}}, "category": "<PERSON>"}, {"key": "010A", "mappings": {"default": {"default": "c ma<PERSON><PERSON> con un punto sopra"}}, "category": "<PERSON>"}, {"key": "010C", "mappings": {"default": {"default": "c ma<PERSON><PERSON> con caron"}}, "category": "<PERSON>"}, {"key": "010E", "mappings": {"default": {"default": "d ma<PERSON><PERSON> con caron"}}, "category": "<PERSON>"}, {"key": "0112", "mappings": {"default": {"default": "e maiuscola con barra sopra"}}, "category": "<PERSON>"}, {"category": "<PERSON>", "key": "0114", "mappings": {"default": {"default": "capitale latina Lettera E con Breve"}}}, {"key": "0116", "mappings": {"default": {"default": "e maiuscola con punto sopra"}}, "category": "<PERSON>"}, {"key": "0118", "mappings": {"default": {"default": "e maiuscola con ogonek"}}, "category": "<PERSON>"}, {"key": "011A", "mappings": {"default": {"default": "e maiuscola con caron"}}, "category": "<PERSON>"}, {"key": "011C", "mappings": {"default": {"default": "g maiuscola con accento circonflesso"}}, "category": "<PERSON>"}, {"key": "011E", "mappings": {"default": {"default": "g maiuscola con segno di vocale corta"}}, "category": "<PERSON>"}, {"key": "0120", "mappings": {"default": {"default": "g maiuscola con punto sopra"}}, "category": "<PERSON>"}, {"key": "0122", "mappings": {"default": {"default": "g maiuscola con cediglia"}}, "category": "<PERSON>"}, {"key": "0124", "mappings": {"default": {"default": "h maiuscola con accento circonflesso"}}, "category": "<PERSON>"}, {"key": "0128", "mappings": {"default": {"default": "i maiuscola con tilde"}}, "category": "<PERSON>"}, {"key": "012A", "mappings": {"default": {"default": "i maiuscola con barra sopra"}}, "category": "<PERSON>"}, {"category": "<PERSON>", "key": "012C", "mappings": {"default": {"default": "lettera maiuscola latina I con breve"}}}, {"key": "012E", "mappings": {"default": {"default": "i maiuscola con ogonek"}}, "category": "<PERSON>"}, {"key": "0130", "mappings": {"default": {"default": "i maiuscola con punto sopra"}}, "category": "<PERSON>"}, {"key": "0134", "mappings": {"default": {"default": "gei maiuscola con accento circonflesso"}}, "category": "<PERSON>"}, {"key": "0136", "mappings": {"default": {"default": "k maiuscola con cediglia"}}, "category": "<PERSON>"}, {"key": "0139", "mappings": {"default": {"default": "l maiuscola con accento acuto"}}, "category": "<PERSON>"}, {"key": "013B", "mappings": {"default": {"default": "l maiuscola con cediglia"}}, "category": "<PERSON>"}, {"key": "013D", "mappings": {"default": {"default": "l maiuscola con caron"}}, "category": "<PERSON>"}, {"key": "013F", "mappings": {"default": {"default": "l maiuscola con un punto nel mezzo"}}, "category": "<PERSON>"}, {"key": "0143", "mappings": {"default": {"default": "n maiuscola con accento acuto"}}, "category": "<PERSON>"}, {"key": "0145", "mappings": {"default": {"default": "n maiuscola con cediglia"}}, "category": "<PERSON>"}, {"key": "0147", "mappings": {"default": {"default": "n maiuscola con caron"}}, "category": "<PERSON>"}, {"key": "014C", "mappings": {"default": {"default": "o maiuscola con barra sopra"}}, "category": "<PERSON>"}, {"category": "<PERSON>", "key": "014E", "mappings": {"default": {"default": "lettera maiuscola latina O con Breve"}}}, {"key": "0150", "mappings": {"default": {"default": "o maiuscola con doppio accento acuto"}}, "category": "<PERSON>"}, {"key": "0154", "mappings": {"default": {"default": "r maiuscola con accento acuto"}}, "category": "<PERSON>"}, {"key": "0156", "mappings": {"default": {"default": "r maiuscola con cediglia"}}, "category": "<PERSON>"}, {"key": "0158", "mappings": {"default": {"default": "r maiuscola con caron"}}, "category": "<PERSON>"}, {"key": "015A", "mappings": {"default": {"default": "s maiuscola con accento acuto"}}, "category": "<PERSON>"}, {"key": "015C", "mappings": {"default": {"default": "s maiuscola s con accento circonflesso"}}, "category": "<PERSON>"}, {"key": "015E", "mappings": {"default": {"default": "s maiuscola s con cediglia"}}, "category": "<PERSON>"}, {"key": "0160", "mappings": {"default": {"default": "s maiuscola con caron"}}, "category": "<PERSON>"}, {"key": "0162", "mappings": {"default": {"default": "t maiuscola con cediglia"}}, "category": "<PERSON>"}, {"key": "0164", "mappings": {"default": {"default": "t maiuscola con caron"}}, "category": "<PERSON>"}, {"key": "0168", "mappings": {"default": {"default": "u maiuscola con tilde"}}, "category": "<PERSON>"}, {"key": "016A", "mappings": {"default": {"default": "u maiuscola con barra sopra"}}, "category": "<PERSON>"}, {"key": "016C", "mappings": {"default": {"default": "u maiuscola con segno di vocale corta"}}, "category": "<PERSON>"}, {"key": "016E", "mappings": {"default": {"default": "u maiuscola con anello sopra"}}, "category": "<PERSON>"}, {"key": "0170", "mappings": {"default": {"default": "u maiuscola con doppio accento acuto"}}, "category": "<PERSON>"}, {"key": "0172", "mappings": {"default": {"default": "u maiuscola con ogonek"}}, "category": "<PERSON>"}, {"key": "0174", "mappings": {"default": {"default": "w maiuscola con accento circonflesso"}}, "category": "<PERSON>"}, {"key": "0176", "mappings": {"default": {"default": "y maiuscola con accento circonflesso"}}, "category": "<PERSON>"}, {"key": "0178", "mappings": {"default": {"default": "y maiuscola with diaeresis"}}, "category": "<PERSON>"}, {"key": "0179", "mappings": {"default": {"default": "z maiuscola con accento acuto"}}, "category": "<PERSON>"}, {"key": "017B", "mappings": {"default": {"default": "z maiuscola con punto sopra"}}, "category": "<PERSON>"}, {"key": "017D", "mappings": {"default": {"default": "z maiuscola con caron"}}, "category": "<PERSON>"}, {"category": "<PERSON>", "key": "01CD", "mappings": {"default": {"default": "lettera maiuscola latina a con Caron"}}}, {"category": "<PERSON>", "key": "01CF", "mappings": {"default": {"default": "lettera maiuscola latina I con Caron"}}}, {"category": "<PERSON>", "key": "01D1", "mappings": {"default": {"default": "lettera maiuscola latina O con <PERSON>"}}}, {"category": "<PERSON>", "key": "01D3", "mappings": {"default": {"default": "carattere latino:  lettera U con Caron"}}}, {"category": "<PERSON>", "key": "01E6", "mappings": {"default": {"default": "lettera maiuscola latina <PERSON>"}}}, {"category": "<PERSON>", "key": "01E8", "mappings": {"default": {"default": "carattere latino:  lettera K con <PERSON>"}}}, {"category": "<PERSON>", "key": "01EA", "mappings": {"default": {"default": "lettera maiuscola latina <PERSON> con O<PERSON>ek"}}}, {"category": "<PERSON>", "key": "01F4", "mappings": {"default": {"default": "latin Capital Letter G with Acute"}}}, {"category": "<PERSON>", "key": "01F8", "mappings": {"default": {"default": "lettera maiuscola latina con tomba"}}}, {"category": "<PERSON>", "key": "0200", "mappings": {"default": {"default": "lettera maiuscola latina a con doppia tomba"}}}, {"category": "<PERSON>", "key": "0202", "mappings": {"default": {"default": "lettera maiuscola latina a con breve invertito"}}}, {"category": "<PERSON>", "key": "0204", "mappings": {"default": {"default": "capitale latina Lettera E con doppia tomba"}}}, {"category": "<PERSON>", "key": "0206", "mappings": {"default": {"default": "capitale latina Lettera E con breve invertito"}}}, {"category": "<PERSON>", "key": "0208", "mappings": {"default": {"default": "capitale latina Lettera I con doppia tomba"}}}, {"category": "<PERSON>", "key": "020A", "mappings": {"default": {"default": "capitale latina Lettera I con Inverted Breve"}}}, {"category": "<PERSON>", "key": "020C", "mappings": {"default": {"default": "capitale latina Lettera O con doppia tomba"}}}, {"category": "<PERSON>", "key": "020E", "mappings": {"default": {"default": "lettera maiuscola latina O con breve invertito"}}}, {"category": "<PERSON>", "key": "0210", "mappings": {"default": {"default": "latin Capital Letter R with Double Grave"}}}, {"category": "<PERSON>", "key": "0212", "mappings": {"default": {"default": "lettera maiuscola latina R con breve invertito"}}}, {"category": "<PERSON>", "key": "0214", "mappings": {"default": {"default": "latin Capital Lettera U con Double Grave"}}}, {"category": "<PERSON>", "key": "0216", "mappings": {"default": {"default": "lettera maiuscola latina U con breve invertito"}}}, {"category": "<PERSON>", "key": "0218", "mappings": {"default": {"default": "S maiuscola con comma sotto"}}}, {"category": "<PERSON>", "key": "021A", "mappings": {"default": {"default": "lettera maiuscola latina T con la virgola sotto"}}}, {"category": "<PERSON>", "key": "021E", "mappings": {"default": {"default": "capitale latina Lettera H con Caron"}}}, {"category": "<PERSON>", "key": "0226", "mappings": {"default": {"default": "lettera maiuscola latina a con Punto Sopra"}}}, {"category": "<PERSON>", "key": "0228", "mappings": {"default": {"default": "capitale latina Lettera E con Cedilla"}}}, {"category": "<PERSON>", "key": "022E", "mappings": {"default": {"default": "lettera maiuscola latina O con Punto Sopra"}}}, {"category": "<PERSON>", "key": "0232", "mappings": {"default": {"default": "capitale latina Lettera Y con Macron"}}}, {"key": "1E00", "mappings": {"default": {"default": "a maiuscola con anello sotto"}}, "category": "<PERSON>"}, {"key": "1E02", "mappings": {"default": {"default": "b ma<PERSON><PERSON> con punto sopra"}}, "category": "<PERSON>"}, {"key": "1E04", "mappings": {"default": {"default": "b ma<PERSON><PERSON> con punto sotto"}}, "category": "<PERSON>"}, {"key": "1E06", "mappings": {"default": {"default": "b ma<PERSON><PERSON> con linea sotto"}}, "category": "<PERSON>"}, {"key": "1E0A", "mappings": {"default": {"default": "d ma<PERSON><PERSON><PERSON> punto sopra"}}, "category": "<PERSON>"}, {"key": "1E0C", "mappings": {"default": {"default": "d ma<PERSON><PERSON> con punto sotto"}}, "category": "<PERSON>"}, {"key": "1E0E", "mappings": {"default": {"default": "d ma<PERSON><PERSON> con linea sotto"}}, "category": "<PERSON>"}, {"key": "1E10", "mappings": {"default": {"default": "d maiuscola con cediglia"}}, "category": "<PERSON>"}, {"key": "1E12", "mappings": {"default": {"default": "d ma<PERSON><PERSON> con accento circonflesso sotto"}}, "category": "<PERSON>"}, {"key": "1E18", "mappings": {"default": {"default": "e maiuscola con accento circonflesso sotto"}}, "category": "<PERSON>"}, {"key": "1E1A", "mappings": {"default": {"default": "e maiuscola con tilde sotto"}}, "category": "<PERSON>"}, {"key": "1E1E", "mappings": {"default": {"default": "f maiuscola con punto sopra"}}, "category": "<PERSON>"}, {"key": "1E20", "mappings": {"default": {"default": "g maiuscola con barra sopra"}}, "category": "<PERSON>"}, {"key": "1E22", "mappings": {"default": {"default": "h maiuscola con punto sopra"}}, "category": "<PERSON>"}, {"key": "1E24", "mappings": {"default": {"default": "h maiuscola con punto sotto"}}, "category": "<PERSON>"}, {"key": "1E26", "mappings": {"default": {"default": "h maiuscola con dieresi"}}, "category": "<PERSON>"}, {"key": "1E28", "mappings": {"default": {"default": "h maiuscola con cediglia"}}, "category": "<PERSON>"}, {"key": "1E2A", "mappings": {"default": {"default": "h maiuscola con segno di vocale corta sotto"}}, "category": "<PERSON>"}, {"key": "1E2C", "mappings": {"default": {"default": "i maiuscola con tilde sotto"}}, "category": "<PERSON>"}, {"key": "1E30", "mappings": {"default": {"default": "k maiuscola con accento acuto"}}, "category": "<PERSON>"}, {"key": "1E32", "mappings": {"default": {"default": "k maiuscola con punto sotto"}}, "category": "<PERSON>"}, {"key": "1E34", "mappings": {"default": {"default": "k maiuscola con linea sotto"}}, "category": "<PERSON>"}, {"key": "1E36", "mappings": {"default": {"default": "l maiuscola con punto sotto"}}, "category": "<PERSON>"}, {"key": "1E3A", "mappings": {"default": {"default": "l maiuscola con linea sotto"}}, "category": "<PERSON>"}, {"key": "1E3C", "mappings": {"default": {"default": "l maiuscola con accento circonflesso sotto"}}, "category": "<PERSON>"}, {"key": "1E3E", "mappings": {"default": {"default": "m maiuscola con accento acuto"}}, "category": "<PERSON>"}, {"key": "1E40", "mappings": {"default": {"default": "m maiuscola con punto sopra"}}, "category": "<PERSON>"}, {"key": "1E42", "mappings": {"default": {"default": "m maiuscola con punto sotto"}}, "category": "<PERSON>"}, {"key": "1E44", "mappings": {"default": {"default": "n maiuscola con punto sopra"}}, "category": "<PERSON>"}, {"key": "1E46", "mappings": {"default": {"default": "n maiuscola con punto sotto"}}, "category": "<PERSON>"}, {"key": "1E48", "mappings": {"default": {"default": "n maiuscola con linea sotto"}}, "category": "<PERSON>"}, {"key": "1E4A", "mappings": {"default": {"default": "n maiuscola con accento circonflesso sotto"}}, "category": "<PERSON>"}, {"key": "1E54", "mappings": {"default": {"default": "p maiuscola con accento acuto"}}, "category": "<PERSON>"}, {"key": "1E56", "mappings": {"default": {"default": "p maiuscola con punto sopra"}}, "category": "<PERSON>"}, {"key": "1E58", "mappings": {"default": {"default": "r maiuscola con punto sopra"}}, "category": "<PERSON>"}, {"key": "1E5A", "mappings": {"default": {"default": "r ma<PERSON><PERSON> con punto sotto"}}, "category": "<PERSON>"}, {"key": "1E5E", "mappings": {"default": {"default": "r ma<PERSON><PERSON> con linea sotto"}}, "category": "<PERSON>"}, {"key": "1E60", "mappings": {"default": {"default": "s maiuscola con punto sopra"}}, "category": "<PERSON>"}, {"key": "1E62", "mappings": {"default": {"default": "s maiuscola con punto sotto"}}, "category": "<PERSON>"}, {"key": "1E6A", "mappings": {"default": {"default": "t maiuscola con punto sopra"}}, "category": "<PERSON>"}, {"key": "1E6C", "mappings": {"default": {"default": "t maius<PERSON> con punto sotto"}}, "category": "<PERSON>"}, {"key": "1E6E", "mappings": {"default": {"default": "t maiuscola con linea sotto"}}, "category": "<PERSON>"}, {"key": "1E70", "mappings": {"default": {"default": "t maiuscola con accento circonflesso sotto"}}, "category": "<PERSON>"}, {"key": "1E72", "mappings": {"default": {"default": "u maiuscola con dieresi sotto"}}, "category": "<PERSON>"}, {"key": "1E74", "mappings": {"default": {"default": "u maiuscola con tilde sotto"}}, "category": "<PERSON>"}, {"key": "1E76", "mappings": {"default": {"default": "u maiuscola con accento circonflesso sotto"}}, "category": "<PERSON>"}, {"key": "1E7C", "mappings": {"default": {"default": "v maiuscola con tilde"}}, "category": "<PERSON>"}, {"key": "1E7E", "mappings": {"default": {"default": "v maiuscola con punto sotto"}}, "category": "<PERSON>"}, {"key": "1E80", "mappings": {"default": {"default": "w maiuscola con accento grave"}}, "category": "<PERSON>"}, {"key": "1E82", "mappings": {"default": {"default": "w maiuscola con accento acuto"}}, "category": "<PERSON>"}, {"key": "1E84", "mappings": {"default": {"default": "w maiuscola con dieresi"}}, "category": "<PERSON>"}, {"key": "1E86", "mappings": {"default": {"default": "w maiuscola con punto sopra"}}, "category": "<PERSON>"}, {"key": "1E88", "mappings": {"default": {"default": "w maiuscola con punto sotto"}}, "category": "<PERSON>"}, {"key": "1E8A", "mappings": {"default": {"default": "x maiuscola con punto sopra"}}, "category": "<PERSON>"}, {"key": "1E8C", "mappings": {"default": {"default": "x maiuscola con dieresi"}}, "category": "<PERSON>"}, {"key": "1E8E", "mappings": {"default": {"default": "y maiuscola con punto sopra"}}, "category": "<PERSON>"}, {"key": "1E90", "mappings": {"default": {"default": "z maiuscola con accento circonflesso"}}, "category": "<PERSON>"}, {"key": "1E92", "mappings": {"default": {"default": "z maiuscola con punto sotto"}}, "category": "<PERSON>"}, {"key": "1E94", "mappings": {"default": {"default": "z maiuscola con linea sotto"}}, "category": "<PERSON>"}, {"key": "1EA0", "mappings": {"default": {"default": "a maiuscola con punto sotto"}}, "category": "<PERSON>"}, {"key": "1EA2", "mappings": {"default": {"default": "a maiuscola con gancio sopra"}}, "category": "<PERSON>"}, {"key": "1EB8", "mappings": {"default": {"default": "e maiuscola con punto sotto"}}, "category": "<PERSON>"}, {"key": "1EBA", "mappings": {"default": {"default": "e maiuscola con gancio sopra"}}, "category": "<PERSON>"}, {"key": "1EBC", "mappings": {"default": {"default": "e maiuscola con tilde"}}, "category": "<PERSON>"}, {"key": "1EC8", "mappings": {"default": {"default": "i maiuscola con gancio sopra"}}, "category": "<PERSON>"}, {"key": "1ECA", "mappings": {"default": {"default": "i maiuscola con punto sotto"}}, "category": "<PERSON>"}, {"key": "1ECC", "mappings": {"default": {"default": "o maiuscola con punto sotto"}}, "category": "<PERSON>"}, {"key": "1ECE", "mappings": {"default": {"default": "o maiuscola con gancio sopra"}}, "category": "<PERSON>"}, {"key": "1EE4", "mappings": {"default": {"default": "u maiuscola con punto sotto"}}, "category": "<PERSON>"}, {"key": "1EE6", "mappings": {"default": {"default": "u maiuscola con gancio sopra"}}, "category": "<PERSON>"}, {"key": "1EF2", "mappings": {"default": {"default": "y maiuscola con accento grave"}}, "category": "<PERSON>"}, {"key": "1EF4", "mappings": {"default": {"default": "y maiuscola con punto sotto"}}, "category": "<PERSON>"}, {"key": "1EF6", "mappings": {"default": {"default": "y maiuscola con gancio sopra"}}, "category": "<PERSON>"}, {"key": "1EF8", "mappings": {"default": {"default": "y maiuscola con tilde"}}, "category": "<PERSON>"}], "it/symbols/math_angles.min": [{"locale": "it"}, {"category": "Sm", "key": "22BE", "mappings": {"default": {"default": "angolo retto con arco"}}}, {"category": "Sm", "key": "237C", "mappings": {"default": {"default": "angolo retto con freccia a zigzag verso il basso"}}}, {"category": "Sm", "key": "27C0", "mappings": {"default": {"default": "angolo tridimensionale"}}}, {"category": "Sm", "key": "299B", "mappings": {"default": {"default": "angolo misurato aperto a sinistra"}}}, {"category": "Sm", "key": "299C", "mappings": {"default": {"default": "variante ad angolo retto con quadrato"}}}, {"category": "Sm", "key": "299D", "mappings": {"default": {"default": "angolo retto misurato con punto"}}}, {"category": "Sm", "key": "299E", "mappings": {"default": {"default": "angolo con S Inside"}}}, {"category": "Sm", "key": "299F", "mappings": {"default": {"default": "angolo acuto"}}}, {"category": "Sm", "key": "29A0", "mappings": {"default": {"default": "angolo sferico aperto a sinistra"}}}, {"category": "Sm", "key": "29A1", "mappings": {"default": {"default": "apertura dell'angolo sferico"}}}, {"category": "Sm", "key": "29A2", "mappings": {"default": {"default": "angolo girato"}}}, {"category": "Sm", "key": "29A3", "mappings": {"default": {"default": "angolo invertito"}}}, {"category": "Sm", "key": "29A4", "mappings": {"default": {"default": "angolo con barra inferiore"}}}, {"category": "Sm", "key": "29A5", "mappings": {"default": {"default": "angolo inverso con barra inferiore"}}}, {"category": "Sm", "key": "29A6", "mappings": {"default": {"default": "apertura obliqua"}}}, {"category": "Sm", "key": "29A7", "mappings": {"default": {"default": "angolo obliquo aperto"}}}, {"category": "Sm", "key": "29A8", "mappings": {"default": {"default": "angolo misurato con il braccio aperto che termina con la freccia rivolta verso l'alto e verso destra"}}}, {"category": "Sm", "key": "29A9", "mappings": {"default": {"default": "angolo misurato con il braccio aperto che termina con la freccia rivolta verso l'alto e verso sinistra"}}}, {"category": "Sm", "key": "29AA", "mappings": {"default": {"default": "angolo misurato con il braccio aperto che termina con la freccia rivolta verso il basso e verso destra"}}}, {"category": "Sm", "key": "29AB", "mappings": {"default": {"default": "angolo misurato con il braccio aperto che termina con la freccia rivolta verso il basso e verso sinistra"}}}, {"category": "Sm", "key": "29AC", "mappings": {"default": {"default": "angolo misurato con il braccio aperto che termina con la freccia rivolta verso destra e verso l'alto"}}}, {"category": "Sm", "key": "29AD", "mappings": {"default": {"default": "angolo misurato con il braccio aperto che termina con la freccia rivolta verso sinistra e verso l'alto"}}}, {"category": "Sm", "key": "29AE", "mappings": {"default": {"default": "angolo misurato con il braccio aperto che termina con la freccia rivolta verso destra e verso il basso"}}}, {"category": "Sm", "key": "29AF", "mappings": {"default": {"default": "angolo misurato con il braccio aperto che termina con la freccia rivolta verso sinistra e verso il basso"}}}], "it/symbols/math_arrows.min": [{"locale": "it"}, {"key": "2190", "mappings": {"default": {"default": "freccia verso sinistra"}}, "category": "Sm"}, {"key": "2191", "mappings": {"default": {"default": "freccia verso l'alto"}}, "category": "Sm"}, {"key": "2192", "mappings": {"default": {"default": "freccia verso destra"}}, "category": "Sm"}, {"key": "2193", "mappings": {"default": {"default": "freccia verso il basso"}}, "category": "Sm"}, {"key": "2194", "mappings": {"default": {"default": "freccia sinistra e destra"}}, "category": "Sm"}, {"key": "2195", "mappings": {"default": {"default": "freccia alto e basso"}}, "category": "So"}, {"key": "2196", "mappings": {"default": {"default": "freccia a nord ovest"}}, "category": "So"}, {"key": "2197", "mappings": {"default": {"default": "freccia a nord est"}}, "category": "So"}, {"key": "2198", "mappings": {"default": {"default": "freccia a sud est"}}, "category": "So"}, {"key": "2199", "mappings": {"default": {"default": "freccia a sud ovest"}}, "category": "So"}, {"key": "219A", "mappings": {"default": {"default": "freccia sinistra con tratto diagonale"}}, "category": "Sm"}, {"key": "219B", "mappings": {"default": {"default": "freccia destra con tratto diagonale"}}, "category": "Sm"}, {"category": "So", "key": "219C", "mappings": {"default": {"default": "freccia d'onda a sinistra"}}}, {"category": "So", "key": "219D", "mappings": {"default": {"default": "freccia d'onda verso destra"}}}, {"key": "219E", "mappings": {"default": {"default": "freccia sinistra con doppia testa"}}, "category": "So"}, {"key": "219F", "mappings": {"default": {"default": "freccia su con doppia testa"}}, "category": "So"}, {"key": "21A0", "mappings": {"default": {"default": "freccia destra con doppia testa"}}, "category": "Sm"}, {"key": "21A1", "mappings": {"default": {"default": "freccia giù con doppia testa"}}, "category": "So"}, {"category": "So", "key": "21A2", "mappings": {"default": {"default": "freccia sinistra con coda"}}}, {"category": "Sm", "key": "21A3", "mappings": {"default": {"default": "freccia destra con coda :  iniezione totale di notazione z"}}}, {"category": "So", "key": "21A4", "mappings": {"default": {"default": "freccia sinistra dalla barra"}}}, {"category": "So", "key": "21A5", "mappings": {"default": {"default": "freccia verso l'alto dalla barra"}}}, {"category": "Sm", "key": "21A6", "mappings": {"default": {"default": "freccia destra dalla barra :  Mappa della notazione z"}}}, {"category": "So", "key": "21A7", "mappings": {"default": {"default": "freccia verso il basso dalla barra :  simbolo di profondità"}}}, {"category": "So", "key": "21A8", "mappings": {"default": {"default": "freccia giù con base"}}}, {"category": "So", "key": "21A9", "mappings": {"default": {"default": "freccia a sinistra con gancio"}}}, {"category": "So", "key": "21AA", "mappings": {"default": {"default": "freccia verso destra con gancio"}}}, {"category": "So", "key": "21AB", "mappings": {"default": {"default": "freccia sinistra con anello"}}}, {"category": "So", "key": "21AC", "mappings": {"default": {"default": "freccia verso destra con anello"}}}, {"category": "So", "key": "21AD", "mappings": {"default": {"default": "freccia dell'onda sinistra destra"}}}, {"category": "Sm", "key": "21AE", "mappings": {"default": {"default": "freccia destra sinistra con tratto"}}}, {"key": "21AF", "mappings": {"default": {"default": "freccia zigzag giù"}}, "category": "So"}, {"category": "So", "key": "21B0", "mappings": {"default": {"default": "freccia verso l'alto con punta verso sinistra"}}}, {"category": "So", "key": "21B1", "mappings": {"default": {"default": "freccia verso l'alto con punta verso destra"}}}, {"category": "So", "key": "21B2", "mappings": {"default": {"default": "freccia verso il basso con punta verso sinistra"}}}, {"category": "So", "key": "21B3", "mappings": {"default": {"default": "freccia verso il basso con punta verso destra"}}}, {"category": "So", "key": "21B4", "mappings": {"default": {"default": "freccia verso destra con angolo verso il basso :  line feed"}}}, {"category": "So", "key": "21B5", "mappings": {"default": {"default": "freccia verso il basso con angolo verso sinistra"}}}, {"category": "So", "key": "21B6", "mappings": {"default": {"default": "freccia semicircolare superiore in senso antiorario"}}}, {"category": "So", "key": "21B7", "mappings": {"default": {"default": "freccia a semicerchio superiore in senso orario"}}}, {"category": "So", "key": "21B8", "mappings": {"default": {"default": "freccia nord ovest alla barra lunga"}}}, {"category": "So", "key": "21B9", "mappings": {"default": {"default": "freccia verso sinistra per barrare la freccia verso destra"}}}, {"category": "So", "key": "21BA", "mappings": {"default": {"default": "freccia circolare aperta in senso antiorario"}}}, {"category": "So", "key": "21BB", "mappings": {"default": {"default": "freccia a cerchio aperto in senso orario"}}}, {"key": "21C4", "mappings": {"default": {"default": "freccia verso destra sopra freccia verso sinistra"}}, "category": "So"}, {"key": "21C5", "mappings": {"default": {"default": "freccia verso l'alto a sinistra di freccia verso il basso"}}, "category": "So"}, {"key": "21C6", "mappings": {"default": {"default": "freccia verso sinistra sopra freccia verso destra"}}, "category": "So"}, {"key": "21C7", "mappings": {"default": {"default": "coppia di frecce verso sinistra"}}, "category": "So"}, {"key": "21C8", "mappings": {"default": {"default": "coppia di frecce verso l'alto"}}, "category": "So"}, {"key": "21C9", "mappings": {"default": {"default": "coppia di frecce verso destra"}}, "category": "So"}, {"key": "21CA", "mappings": {"default": {"default": "coppia di frecce verso il basso"}}, "category": "So"}, {"key": "21CD", "mappings": {"default": {"default": "freccia doppia verso sinistra con tratto diagonale"}}, "category": "So"}, {"key": "21CE", "mappings": {"default": {"default": "freccia doppia sinistra destra con tratto diagonale"}}, "category": "Sm"}, {"key": "21CF", "mappings": {"default": {"default": "freccia doppia verso destra con tratto diagonale"}}, "category": "Sm"}, {"category": "So", "key": "21D0", "mappings": {"default": {"default": "freccia doppia sinistra"}}}, {"key": "21D1", "mappings": {"default": {"default": "freccia doppia verso l'alto"}}, "category": "So"}, {"key": "21D2", "mappings": {"default": {"default": "freccia doppia verso destra"}}, "category": "Sm"}, {"key": "21D3", "mappings": {"default": {"default": "freccia doppia verso il basso"}}, "category": "So"}, {"key": "21D4", "mappings": {"default": {"default": "freccia doppia sinistra destra"}}, "category": "Sm"}, {"key": "21D5", "mappings": {"default": {"default": "freccia doppia su e giù"}}, "category": "So"}, {"category": "So", "key": "21D6", "mappings": {"default": {"default": "doppia freccia nord ovest"}}}, {"category": "So", "key": "21D7", "mappings": {"default": {"default": "doppia freccia nord est"}}}, {"category": "So", "key": "21D8", "mappings": {"default": {"default": "doppia freccia sud est"}}}, {"category": "So", "key": "21D9", "mappings": {"default": {"default": "doppia freccia sud ovest"}}}, {"key": "21DA", "mappings": {"default": {"default": "freccia triple verso sinistra"}}, "category": "So"}, {"key": "21DB", "mappings": {"default": {"default": "freccia triple verso destra"}}, "category": "So"}, {"category": "So", "key": "21DC", "mappings": {"default": {"default": "freccia ondulata verso sinistra"}}}, {"category": "So", "key": "21DD", "mappings": {"default": {"default": "freccia destra"}}}, {"category": "So", "key": "21DE", "mappings": {"default": {"default": "freccia verso l'alto con doppio tratto"}}}, {"category": "So", "key": "21DF", "mappings": {"default": {"default": "freccia verso il basso con doppio tratto"}}}, {"category": "So", "key": "21E0", "mappings": {"default": {"default": "freccia tratteggiata a sinistra"}}}, {"category": "So", "key": "21E1", "mappings": {"default": {"default": "freccia tratteggiata verso l'alto"}}}, {"category": "So", "key": "21E2", "mappings": {"default": {"default": "freccia tratteggiata a destra"}}}, {"category": "So", "key": "21E3", "mappings": {"default": {"default": "freccia tratteggiata verso il basso"}}}, {"category": "So", "key": "21E4", "mappings": {"default": {"default": "tab a sinistra"}}}, {"category": "So", "key": "21E5", "mappings": {"default": {"default": "freccia verso destra verso il basso"}}}, {"category": "So", "key": "21E6", "mappings": {"default": {"default": "freccia bianca verso sinistra"}}}, {"category": "So", "key": "21E7", "mappings": {"default": {"default": "freccia bianca verso l'alto"}}}, {"category": "So", "key": "21E8", "mappings": {"default": {"default": "freccia bianca rivolta verso destra"}}}, {"category": "So", "key": "21E9", "mappings": {"default": {"default": "freccia bianca verso il basso"}}}, {"category": "So", "key": "21EA", "mappings": {"default": {"default": "freccia verso l'alto bianca da Bar"}}}, {"category": "So", "key": "21EB", "mappings": {"default": {"default": "freccia verso l'alto bianca sul piedistallo"}}}, {"category": "So", "key": "21EC", "mappings": {"default": {"default": "freccia verso l'alto bianca sul piedistallo con barra orizzontale"}}}, {"category": "So", "key": "21ED", "mappings": {"default": {"default": "freccia verso l'alto bianca sul piedistallo con barra verticale"}}}, {"category": "So", "key": "21EE", "mappings": {"default": {"default": "freccia doppia bianca verso l'alto"}}}, {"category": "So", "key": "21EF", "mappings": {"default": {"default": "freccia verso l'alto bianca doppia sul piedistallo"}}}, {"category": "So", "key": "21F0", "mappings": {"default": {"default": "freccia bianca rivolta verso destra"}}}, {"category": "So", "key": "21F1", "mappings": {"default": {"default": "freccia nord ovest all'angolo"}}}, {"category": "So", "key": "21F2", "mappings": {"default": {"default": "freccia sud est all'angolo"}}}, {"category": "So", "key": "21F3", "mappings": {"default": {"default": "freccia bianca in su"}}}, {"category": "Sm", "key": "21F4", "mappings": {"default": {"default": "freccia destra con piccolo cerchio"}}}, {"category": "Sm", "key": "21F5", "mappings": {"default": {"default": "freccia verso il basso freccia verso il basso"}}}, {"category": "Sm", "key": "21F6", "mappings": {"default": {"default": "tre frecce a destra"}}}, {"category": "Sm", "key": "21F7", "mappings": {"default": {"default": "freccia a sinistra con tratto verticale"}}}, {"category": "Sm", "key": "21F8", "mappings": {"default": {"default": "freccia verso destra con tratto verticale"}}}, {"category": "Sm", "key": "21F9", "mappings": {"default": {"default": "freccia destra sinistra con tratto verticale"}}}, {"category": "Sm", "key": "21FA", "mappings": {"default": {"default": "freccia a sinistra con doppio tratto verticale"}}}, {"category": "Sm", "key": "21FB", "mappings": {"default": {"default": "freccia verso destra con doppio tratto verticale"}}}, {"category": "Sm", "key": "21FC", "mappings": {"default": {"default": "freccia destra sinistra con doppio tratto verticale"}}}, {"category": "Sm", "key": "21FD", "mappings": {"default": {"default": "freccia a testa aperta rivolta a sinistra"}}}, {"category": "Sm", "key": "21FE", "mappings": {"default": {"default": "freccia a testa aperta rivolta verso destra"}}}, {"category": "Sm", "key": "21FF", "mappings": {"default": {"default": "freccia a freccia aperta sinistra destra"}}}, {"category": "So", "key": "2301", "mappings": {"default": {"default": "freccia elettrica"}}}, {"category": "So", "key": "2303", "mappings": {"default": {"default": "freccia in su"}}}, {"category": "So", "key": "2304", "mappings": {"default": {"default": "freccia giù"}}}, {"category": "So", "key": "2324", "mappings": {"default": {"default": "freccia su tra due barre oriz<PERSON>i"}}}, {"category": "So", "key": "238B", "mappings": {"default": {"default": "cerchio spezzato con freccia nord ovest"}}}, {"category": "So", "key": "2794", "mappings": {"default": {"default": "freccia verso destra con ampia freccia larga"}}}, {"category": "So", "key": "2798", "mappings": {"default": {"default": "pesante freccia del sud est"}}}, {"category": "So", "key": "2799", "mappings": {"default": {"default": "freccia verso destra pesante"}}}, {"category": "So", "key": "279A", "mappings": {"default": {"default": "freccia nord est pesante"}}}, {"category": "So", "key": "279B", "mappings": {"default": {"default": "freccia verso destra del punto di disegno"}}}, {"category": "So", "key": "279C", "mappings": {"default": {"default": "freccia destra con punta arrotondata pesante"}}}, {"category": "So", "key": "279D", "mappings": {"default": {"default": "freccia verso destra con la freccia a triangolo"}}}, {"category": "So", "key": "279E", "mappings": {"default": {"default": "freccia verso destra con freccia triangolare pesante"}}}, {"category": "So", "key": "279F", "mappings": {"default": {"default": "freccia verso destra con freccia a triangolo tratteggiata"}}}, {"category": "So", "key": "27A0", "mappings": {"default": {"default": "freccia rivolta verso il basso a punta triangolare con tratteggio pesante"}}}, {"category": "So", "key": "27A1", "mappings": {"default": {"default": "freccia destra nera"}}}, {"category": "So", "key": "27A2", "mappings": {"default": {"default": "freccia rivolta verso destra a tre punte superiore tridimensionale"}}}, {"category": "So", "key": "27A3", "mappings": {"default": {"default": "freccia rivolta verso destra a tre punte inferiore"}}}, {"category": "So", "key": "27A4", "mappings": {"default": {"default": "freccia destra nera"}}}, {"category": "So", "key": "27A5", "mappings": {"default": {"default": "freccia nera pesante rivolta verso il basso e verso destra"}}}, {"category": "So", "key": "27A6", "mappings": {"default": {"default": "freccia nera curva verso l'alto e verso destra"}}}, {"category": "So", "key": "27A7", "mappings": {"default": {"default": "freccia nera tozzo verso destra"}}}, {"category": "So", "key": "27A8", "mappings": {"default": {"default": "freccia verso destra nera con punta concava pesante"}}}, {"category": "So", "key": "27A9", "mappings": {"default": {"default": "freccia destra rivolta verso destra bianca"}}}, {"category": "So", "key": "27AA", "mappings": {"default": {"default": "freccia destra rivolta verso sinistra bianca"}}}, {"category": "So", "key": "27AB", "mappings": {"default": {"default": "freccia rivolta verso il basso bianca inclinata all'indietro"}}}, {"category": "So", "key": "27AC", "mappings": {"default": {"default": "freccia rivolta verso destra bianca con ombra inclinata nella parte anteriore"}}}, {"category": "So", "key": "27AD", "mappings": {"default": {"default": "freccia destra bianca in basso a destra in ombra"}}}, {"category": "So", "key": "27AE", "mappings": {"default": {"default": "freccia in alto a destra in alto a destra bianca in ombra"}}}, {"category": "So", "key": "27AF", "mappings": {"default": {"default": "freccia rivolta verso destra, in basso a destra, con ombra bianca"}}}, {"category": "So", "key": "27B1", "mappings": {"default": {"default": "freccia in alto a destra in alto a destra in bianco"}}}, {"category": "So", "key": "27B2", "mappings": {"default": {"default": "freccia destra bianca con cerchio pesante"}}}, {"category": "So", "key": "27B3", "mappings": {"default": {"default": "freccia verso destra con piume bianche"}}}, {"category": "So", "key": "27B4", "mappings": {"default": {"default": "freccia sud orientale con piume nere"}}}, {"category": "So", "key": "27B5", "mappings": {"default": {"default": "freccia verso il basso con piume nere"}}}, {"category": "So", "key": "27B6", "mappings": {"default": {"default": "freccia nord orientale con piume nere"}}}, {"category": "So", "key": "27B7", "mappings": {"default": {"default": "freccia sud orientale con grandi piume nere"}}}, {"category": "So", "key": "27B8", "mappings": {"default": {"default": "freccia verso il basso con piume nere pesanti"}}}, {"category": "So", "key": "27B9", "mappings": {"default": {"default": "freccia nord est con piume nere pesanti"}}}, {"category": "So", "key": "27BA", "mappings": {"default": {"default": "freccia rivolta verso il basso a goccia a goccia"}}}, {"category": "So", "key": "27BB", "mappings": {"default": {"default": "freccia destra con punta a goccia pesante"}}}, {"category": "So", "key": "27BC", "mappings": {"default": {"default": "freccia verso destra con coda a cuneo"}}}, {"category": "So", "key": "27BD", "mappings": {"default": {"default": "freccia verso destra con coda a cuneo"}}}, {"category": "So", "key": "27BE", "mappings": {"default": {"default": "freccia verso destra delineata aperta"}}}, {"category": "Sm", "key": "27F0", "mappings": {"default": {"default": "freccia quadrupla verso l'alto"}}}, {"category": "Sm", "key": "27F1", "mappings": {"default": {"default": "freccia quadrupla verso il basso"}}}, {"category": "Sm", "key": "27F2", "mappings": {"default": {"default": "freccia Cerchio Gapped in senso antiorario"}}}, {"category": "Sm", "key": "27F3", "mappings": {"default": {"default": "freccia circolare con gapping in senso orario"}}}, {"category": "Sm", "key": "27F4", "mappings": {"default": {"default": "freccia destra con cerchiata più"}}}, {"category": "Sm", "key": "27F5", "mappings": {"default": {"default": "freccia sinistra verso sinistra"}}}, {"category": "Sm", "key": "27F6", "mappings": {"default": {"default": "freccia verso destra lunga"}}}, {"category": "Sm", "key": "27F7", "mappings": {"default": {"default": "freccia sinistra sinistra lunga"}}}, {"category": "Sm", "key": "27F8", "mappings": {"default": {"default": "doppia freccia a sinistra lungo"}}}, {"category": "Sm", "key": "27F9", "mappings": {"default": {"default": "doppia freccia lunga destra"}}}, {"category": "Sm", "key": "27FA", "mappings": {"default": {"default": "doppia freccia destra sinistra lunga"}}}, {"category": "Sm", "key": "27FB", "mappings": {"default": {"default": "freccia sinistra verso sinistra dalla barra"}}}, {"category": "Sm", "key": "27FC", "mappings": {"default": {"default": "freccia destra verso destra dalla barra"}}}, {"category": "Sm", "key": "27FD", "mappings": {"default": {"default": "freccia a sinistra lunga doppia da barra"}}}, {"category": "Sm", "key": "27FE", "mappings": {"default": {"default": "freccia destra lunga doppia da barra"}}}, {"category": "Sm", "key": "27FF", "mappings": {"default": {"default": "freccia ondulata lunga destra"}}}, {"category": "Sm", "key": "2900", "mappings": {"default": {"default": "freccia a due punte a destra con tratto verticale"}}}, {"category": "Sm", "key": "2901", "mappings": {"default": {"default": "freccia a due punte rivolta verso destra con doppio tratto verticale"}}}, {"category": "Sm", "key": "2902", "mappings": {"default": {"default": "freccia sinistra a sinistra con tratto verticale"}}}, {"category": "Sm", "key": "2903", "mappings": {"default": {"default": "doppia freccia a destra con tratto verticale"}}}, {"category": "Sm", "key": "2904", "mappings": {"default": {"default": "freccia destra sinistra doppia con tratto verticale"}}}, {"category": "Sm", "key": "2905", "mappings": {"default": {"default": "freccia a due punte destra dalla barra"}}}, {"category": "Sm", "key": "2906", "mappings": {"default": {"default": "freccia sinistra a sinistra di Bar"}}}, {"category": "Sm", "key": "2907", "mappings": {"default": {"default": "a destra doppia freccia da bar"}}}, {"category": "Sm", "key": "2908", "mappings": {"default": {"default": "freccia verso il basso con tratto orizzontale"}}}, {"category": "Sm", "key": "2909", "mappings": {"default": {"default": "freccia verso l'alto con tratto orizzontale"}}}, {"category": "Sm", "key": "290A", "mappings": {"default": {"default": "tripla freccia verso l'alto"}}}, {"category": "Sm", "key": "290B", "mappings": {"default": {"default": "freccia tripla verso il basso"}}}, {"category": "Sm", "key": "290C", "mappings": {"default": {"default": "freccia Double Dash sinistra"}}}, {"category": "Sm", "key": "290D", "mappings": {"default": {"default": "freccia Double Dash rivolta verso destra"}}}, {"category": "Sm", "key": "290E", "mappings": {"default": {"default": "freccia tripla a sinistra"}}}, {"category": "Sm", "key": "290F", "mappings": {"default": {"default": "freccia Triple Dash rivolta verso destra"}}}, {"category": "Sm", "key": "2910", "mappings": {"default": {"default": "freccia tripla a due punte a destra"}}}, {"category": "Sm", "key": "2911", "mappings": {"default": {"default": "freccia verso destra con stelo tratteggiato"}}}, {"category": "Sm", "key": "2912", "mappings": {"default": {"default": "freccia verso l'alto da barrare"}}}, {"category": "Sm", "key": "2913", "mappings": {"default": {"default": "freccia verso il basso verso il basso"}}}, {"category": "Sm", "key": "2914", "mappings": {"default": {"default": "freccia verso destra con coda con tratto verticale"}}}, {"category": "Sm", "key": "2915", "mappings": {"default": {"default": "freccia destra con coda con doppio tratto verticale"}}}, {"category": "Sm", "key": "2916", "mappings": {"default": {"default": "freccia a due punte destra con coda"}}}, {"category": "Sm", "key": "2917", "mappings": {"default": {"default": "freccia a due punte a destra con coda con tratto verticale"}}}, {"category": "Sm", "key": "2918", "mappings": {"default": {"default": "freccia a due punte destra con coda con doppio tratto verticale"}}}, {"category": "Sm", "key": "2919", "mappings": {"default": {"default": "freccia a sinistra"}}}, {"category": "Sm", "key": "291A", "mappings": {"default": {"default": "freccia a destra"}}}, {"category": "Sm", "key": "291B", "mappings": {"default": {"default": "freccia a sinistra a doppia freccia"}}}, {"category": "Sm", "key": "291C", "mappings": {"default": {"default": "doppia freccia a destra"}}}, {"category": "Sm", "key": "291D", "mappings": {"default": {"default": "freccia verso sinistra al diamante nero"}}}, {"category": "Sm", "key": "291E", "mappings": {"default": {"default": "freccia verso destra per Black Diamond"}}}, {"category": "Sm", "key": "291F", "mappings": {"default": {"default": "freccia sinistra da Bar a diamante nero"}}}, {"category": "Sm", "key": "2920", "mappings": {"default": {"default": "freccia verso destra da Bar a Black Diamond"}}}, {"category": "Sm", "key": "2921", "mappings": {"default": {"default": "freccia nord occidentale e sud orientale"}}}, {"category": "Sm", "key": "2922", "mappings": {"default": {"default": "freccia nord est e sud ovest"}}}, {"category": "Sm", "key": "2923", "mappings": {"default": {"default": "freccia nord ovest con gancio"}}}, {"category": "Sm", "key": "2924", "mappings": {"default": {"default": "freccia nord est con gancio"}}}, {"category": "Sm", "key": "2925", "mappings": {"default": {"default": "freccia sud orientale con gancio"}}}, {"category": "Sm", "key": "2926", "mappings": {"default": {"default": "freccia sud ovest con gancio"}}}, {"category": "Sm", "key": "2927", "mappings": {"default": {"default": "freccia nord occidentale e freccia nord orientale"}}}, {"category": "Sm", "key": "2928", "mappings": {"default": {"default": "freccia nord orientale e freccia sud orientale"}}}, {"category": "Sm", "key": "2929", "mappings": {"default": {"default": "freccia sud orientale e freccia sud occidentale"}}}, {"category": "Sm", "key": "292A", "mappings": {"default": {"default": "freccia sud occidentale e freccia nord occidentale"}}}, {"category": "Sm", "key": "292D", "mappings": {"default": {"default": "freccia sud orientale che attraversa la freccia nord est"}}}, {"category": "Sm", "key": "292E", "mappings": {"default": {"default": "freccia nord orientale che attraversa la freccia sud orientale"}}}, {"category": "Sm", "key": "292F", "mappings": {"default": {"default": "caduta diagonale che attraversa la freccia di nord est"}}}, {"category": "Sm", "key": "2930", "mappings": {"default": {"default": "aumento diagonale che attraversa la freccia sud orientale"}}}, {"category": "Sm", "key": "2931", "mappings": {"default": {"default": "freccia nord orientale che attraversa la freccia nord occidentale"}}}, {"category": "Sm", "key": "2932", "mappings": {"default": {"default": "freccia nord ovest freccia nord est"}}}, {"category": "Sm", "key": "2933", "mappings": {"default": {"default": "freccia d'onda che punta direttamente a destra"}}}, {"category": "Sm", "key": "2934", "mappings": {"default": {"default": "freccia che punta verso destra e poi verso l'alto"}}}, {"category": "Sm", "key": "2935", "mappings": {"default": {"default": "freccia che punta verso destra e poi curva verso il basso"}}}, {"category": "Sm", "key": "2936", "mappings": {"default": {"default": "freccia che punta verso il basso e poi curva verso sinistra"}}}, {"category": "Sm", "key": "2937", "mappings": {"default": {"default": "freccia che punta verso il basso e poi curva verso destra"}}}, {"category": "Sm", "key": "2938", "mappings": {"default": {"default": "freccia destra dell'arco destro"}}}, {"category": "Sm", "key": "2939", "mappings": {"default": {"default": "freccia sinistra dell'arco sinistro"}}}, {"category": "Sm", "key": "293A", "mappings": {"default": {"default": "freccia in senso antiorario superiore"}}}, {"category": "Sm", "key": "293B", "mappings": {"default": {"default": "freccia in basso. freccia in senso antiorario"}}}, {"category": "Sm", "key": "293C", "mappings": {"default": {"default": "freccia in senso orario con meno"}}}, {"category": "Sm", "key": "293D", "mappings": {"default": {"default": "freccia in senso antiorario superiore con più"}}}, {"category": "Sm", "key": "293E", "mappings": {"default": {"default": "freccia semicircolare in senso orario in basso a destra"}}}, {"category": "Sm", "key": "293F", "mappings": {"default": {"default": "freccia sinistra in senso antiorario semicircolare in basso a sinistra"}}}, {"category": "Sm", "key": "2940", "mappings": {"default": {"default": "freccia circolare chiusa in senso antiorario"}}}, {"category": "Sm", "key": "2941", "mappings": {"default": {"default": "freccia circolare chiusa in senso orario"}}}, {"category": "Sm", "key": "2942", "mappings": {"default": {"default": "freccia destra sopra freccia corta sinistra"}}}, {"category": "Sm", "key": "2943", "mappings": {"default": {"default": "freccia sinistra verso destra freccia verso destra corta"}}}, {"category": "Sm", "key": "2944", "mappings": {"default": {"default": "freccia verso destra corta sopra la freccia verso sinistra"}}}, {"category": "Sm", "key": "2945", "mappings": {"default": {"default": "freccia verso destra con più sotto"}}}, {"category": "Sm", "key": "2946", "mappings": {"default": {"default": "freccia a sinistra con più sotto"}}}, {"category": "Sm", "key": "2947", "mappings": {"default": {"default": "freccia verso destra attraverso X"}}}, {"category": "Sm", "key": "2948", "mappings": {"default": {"default": "freccia destra sinistra attraverso il piccolo cerchio"}}}, {"category": "Sm", "key": "2949", "mappings": {"default": {"default": "freccia a due punte verso l'alto del piccolo cerchio"}}}, {"key": "2970", "mappings": {"default": {"default": "round implies"}}, "category": "Sm"}, {"category": "Sm", "key": "2971", "mappings": {"default": {"default": "uguale segno sopra la freccia verso destra"}}}, {"category": "Sm", "key": "2972", "mappings": {"default": {"default": "operatore di tilde sopra la freccia verso destra"}}}, {"category": "Sm", "key": "2973", "mappings": {"default": {"default": "freccia verso sinistra sopra l'operatore Tilde"}}}, {"category": "Sm", "key": "2974", "mappings": {"default": {"default": "freccia verso destra sopra l'operatore Tilde"}}}, {"key": "2975", "mappings": {"default": {"default": "freccia a destra sopra a uguale circa a"}}, "category": "Sm"}, {"category": "Sm", "key": "2976", "mappings": {"default": {"default": "freccia inferiore a sinistra"}}}, {"category": "Sm", "key": "2977", "mappings": {"default": {"default": "freccia sinistra verso il basso"}}}, {"category": "Sm", "key": "2978", "mappings": {"default": {"default": "freccia maggiore a quella sopra a destra"}}}, {"category": "Sm", "key": "2979", "mappings": {"default": {"default": "sottoinsieme sopra la freccia verso destra"}}}, {"category": "Sm", "key": "297A", "mappings": {"default": {"default": "freccia verso sinistra attraverso il sottoinsieme"}}}, {"category": "Sm", "key": "297B", "mappings": {"default": {"default": "superset sopra la freccia sinistra"}}}, {"category": "Sm", "key": "29B3", "mappings": {"default": {"default": "insieme vuoto con la freccia destra qui sopra"}}}, {"category": "Sm", "key": "29B4", "mappings": {"default": {"default": "set vuoto con freccia sinistra qui sopra"}}}, {"category": "Sm", "key": "29BD", "mappings": {"default": {"default": "freccia su cerchio"}}}, {"category": "Sm", "key": "29EA", "mappings": {"default": {"default": "black Diamond con freccia giù"}}}, {"category": "Sm", "key": "29EC", "mappings": {"default": {"default": "cerchio bianco con freccia giù"}}}, {"category": "Sm", "key": "29ED", "mappings": {"default": {"default": "cerchio nero con freccia giù"}}}, {"category": "Sm", "key": "2A17", "mappings": {"default": {"default": "integrale con freccia sinistra con gancio"}}}, {"category": "So", "key": "2B00", "mappings": {"default": {"default": "freccia bianca nord orientale"}}}, {"category": "So", "key": "2B01", "mappings": {"default": {"default": "freccia bianca del nord ovest"}}}, {"category": "So", "key": "2B02", "mappings": {"default": {"default": "freccia bianca sud orientale"}}}, {"category": "So", "key": "2B03", "mappings": {"default": {"default": "freccia bianca sud occidentale"}}}, {"category": "So", "key": "2B04", "mappings": {"default": {"default": "freccia bianca sinistra destra"}}}, {"category": "So", "key": "2B05", "mappings": {"default": {"default": "freccia nera verso sinistra"}}}, {"category": "So", "key": "2B06", "mappings": {"default": {"default": "freccia nera verso l'alto"}}}, {"category": "So", "key": "2B07", "mappings": {"default": {"default": "freccia nera verso il basso"}}}, {"category": "So", "key": "2B08", "mappings": {"default": {"default": "freccia nera nord orientale"}}}, {"category": "So", "key": "2B09", "mappings": {"default": {"default": "freccia nera del nord ovest"}}}, {"category": "So", "key": "2B0A", "mappings": {"default": {"default": "freccia nera a sud est"}}}, {"category": "So", "key": "2B0B", "mappings": {"default": {"default": "freccia nera sud occidentale"}}}, {"category": "So", "key": "2B0C", "mappings": {"default": {"default": "freccia nera sinistra destra"}}}, {"category": "So", "key": "2B0D", "mappings": {"default": {"default": "freccia nera in su"}}}, {"category": "So", "key": "2B0E", "mappings": {"default": {"default": "freccia verso destra con punta verso il basso"}}}, {"category": "So", "key": "2B0F", "mappings": {"default": {"default": "freccia verso destra con la punta verso l'alto"}}}, {"category": "So", "key": "2B10", "mappings": {"default": {"default": "freccia a sinistra con punta verso il basso"}}}, {"category": "So", "key": "2B11", "mappings": {"default": {"default": "freccia verso sinistra con punta verso l'alto"}}}, {"category": "Sm", "key": "2B30", "mappings": {"default": {"default": "freccia sinistra con piccolo cerchio"}}}, {"category": "Sm", "key": "2B31", "mappings": {"default": {"default": "tre frecce a sinistra"}}}, {"category": "Sm", "key": "2B32", "mappings": {"default": {"default": "freccia sinistra con cerchiata più"}}}, {"category": "Sm", "key": "2B33", "mappings": {"default": {"default": "freccia ondulata lunga sinistra"}}}, {"category": "Sm", "key": "2B34", "mappings": {"default": {"default": "freccia a due punte a sinistra con tratto verticale"}}}, {"category": "Sm", "key": "2B35", "mappings": {"default": {"default": "freccia a due punte a sinistra con doppio tratto verticale"}}}, {"category": "Sm", "key": "2B36", "mappings": {"default": {"default": "freccia a due punte a sinistra da barra"}}}, {"category": "Sm", "key": "2B37", "mappings": {"default": {"default": "freccia a triplo tratteggio a sinistra a due punte"}}}, {"category": "Sm", "key": "2B38", "mappings": {"default": {"default": "freccia a sinistra con stelo tratteggiato"}}}, {"category": "Sm", "key": "2B39", "mappings": {"default": {"default": "freccia sinistra con coda con tratto verticale"}}}, {"category": "Sm", "key": "2B3A", "mappings": {"default": {"default": "freccia sinistra con coda con doppio tratto verticale"}}}, {"category": "Sm", "key": "2B3B", "mappings": {"default": {"default": "freccia a due punte a sinistra con coda"}}}, {"category": "Sm", "key": "2B3C", "mappings": {"default": {"default": "freccia a due punte a sinistra con coda con tratto verticale"}}}, {"category": "Sm", "key": "2B3D", "mappings": {"default": {"default": "freccia a due punte a sinistra con coda con doppio tratto verticale"}}}, {"category": "Sm", "key": "2B3E", "mappings": {"default": {"default": "freccia verso sinistra attraverso X"}}}, {"category": "Sm", "key": "2B3F", "mappings": {"default": {"default": "freccia d'onda che punta direttamente a sinistra"}}}, {"category": "Sm", "key": "2B40", "mappings": {"default": {"default": "segno uguale sopra freccia sinistra"}}}, {"category": "Sm", "key": "2B41", "mappings": {"default": {"default": "operatore Tilde inverso sopra la freccia sinistra"}}}, {"category": "Sm", "key": "2B42", "mappings": {"default": {"default": "freccia sinistra verso sinistra al contrario quasi uguale a"}}}, {"category": "Sm", "key": "2B43", "mappings": {"default": {"default": "freccia verso destra per maggiore di"}}}, {"category": "Sm", "key": "2B44", "mappings": {"default": {"default": "freccia verso destra attraverso il Superset"}}}, {"category": "So", "key": "2B45", "mappings": {"default": {"default": "freccia quadrupla a sinistra"}}}, {"category": "So", "key": "2B46", "mappings": {"default": {"default": "freccia quadrupla rivolta verso destra"}}}, {"category": "Sm", "key": "2B47", "mappings": {"default": {"default": "tilde inverso sopra freccia verso destra"}}}, {"category": "Sm", "key": "2B48", "mappings": {"default": {"default": "freccia verso destra sopra il retromarcia quasi uguale a"}}}, {"category": "Sm", "key": "2B49", "mappings": {"default": {"default": "operatore Tilde sopra freccia sinistra"}}}, {"category": "Sm", "key": "2B4A", "mappings": {"default": {"default": "freccia sinistra verso sinistra quasi uguale a"}}}, {"category": "Sm", "key": "2B4B", "mappings": {"default": {"default": "freccia a sinistra sopra l'operatore Tilde inverso"}}}, {"category": "Sm", "key": "2B4C", "mappings": {"default": {"default": "freccia verso destra sopra l'operatore Tilde inverso"}}}, {"category": "Sm", "key": "FFE9", "mappings": {"default": {"default": "freccia a sinistra a metà campo"}}}, {"category": "Sm", "key": "FFEA", "mappings": {"default": {"default": "freccia verso l'alto a metà larghezza"}}}, {"category": "Sm", "key": "FFEB", "mappings": {"default": {"default": "freccia verso la metà a destra"}}}, {"category": "Sm", "key": "FFEC", "mappings": {"default": {"default": "freccia verso il basso a metà larghezza"}}}], "it/symbols/math_characters.min": [{"locale": "it"}, {"category": "Ll", "key": "2113", "mappings": {"default": {"default": "script l minuscola"}}}, {"category": "Sm", "key": "2118", "mappings": {"default": {"default": "script P <PERSON>"}}}, {"category": "Ll", "key": "213C", "mappings": {"default": {"default": "pi a grassetto da lavagna"}}}, {"category": "Ll", "key": "213D", "mappings": {"default": {"default": "gamma a grassetto da lavagna"}}}, {"category": "<PERSON>", "key": "213E", "mappings": {"default": {"default": "gamma del capitale a grassetto da lavagna"}}}, {"category": "<PERSON>", "key": "213F", "mappings": {"default": {"default": "<PERSON> maius<PERSON>lo a grassetto da lavagna"}}}, {"category": "Sm", "key": "2140", "mappings": {"default": {"default": "sommatoria a grassetto da lavagna"}}}, {"category": "<PERSON>", "key": "2145", "mappings": {"default": {"default": "d ma<PERSON><PERSON> italico a grassetto da lavagna"}}}, {"category": "Ll", "key": "2146", "mappings": {"default": {"default": "d italico a grassetto da lavagna"}}}, {"category": "Ll", "key": "2147", "mappings": {"default": {"default": "e italico a grassetto da lavagna"}}}, {"category": "Ll", "key": "2148", "mappings": {"default": {"default": "i italico a grassetto da lavagna"}}}, {"category": "Ll", "key": "2149", "mappings": {"default": {"default": "j italico a grassetto da lavagna"}}}, {"category": "Ll", "key": "1D6A4", "mappings": {"default": {"default": "i senza punto"}}}, {"category": "Ll", "key": "1D6A5", "mappings": {"default": {"default": "j senza punto"}}}], "it/symbols/math_delimiters.min": [{"locale": "it"}, {"key": "0028", "mappings": {"default": {"default": "parentesi tonda aperta"}}, "category": "Ps"}, {"key": "0029", "mappings": {"default": {"default": "parentesi tonda chiusa"}}, "category": "Pe"}, {"key": "005B", "mappings": {"default": {"default": "parentesi quadra aperta"}}, "category": "Ps"}, {"key": "005D", "mappings": {"default": {"default": "parentesi quadra chiusa"}}, "category": "Pe"}, {"key": "007B", "mappings": {"default": {"default": "parentesi graffa aperta"}}, "category": "Ps"}, {"key": "007D", "mappings": {"default": {"default": "parentesi graffa chiusa"}}, "category": "Pe"}, {"category": "Ps", "key": "2045", "mappings": {"default": {"default": "parentesi quadrata sinistra con penna"}}}, {"category": "Pe", "key": "2046", "mappings": {"default": {"default": "parentesi quadrata destra con penna"}}}, {"category": "Sm", "key": "2308", "mappings": {"default": {"default": "parentesi sinistra di arrotondamento per eccesso"}}}, {"category": "Sm", "key": "2309", "mappings": {"default": {"default": "parentesi destra di arrotondamento per eccesso"}}}, {"category": "Sm", "key": "230A", "mappings": {"default": {"default": "parentesi sinistra di arrotondamento per difetto"}}}, {"category": "Sm", "key": "230B", "mappings": {"default": {"default": "parentesi destra di arrotondamento per difetto"}}}, {"category": "So", "key": "230C", "mappings": {"default": {"default": "crop in basso a destra"}}}, {"category": "So", "key": "230D", "mappings": {"default": {"default": "crop in basso a sinistra"}}}, {"category": "So", "key": "230E", "mappings": {"default": {"default": "crop in alto a destra"}}}, {"category": "So", "key": "230F", "mappings": {"default": {"default": "crop in alto a sinistra"}}}, {"category": "So", "key": "231C", "mappings": {"default": {"default": "angolo in alto a sinistra"}}}, {"category": "So", "key": "231D", "mappings": {"default": {"default": "angolo in alto a destra"}}}, {"category": "So", "key": "231E", "mappings": {"default": {"default": "angolo in basso a sinistra"}}}, {"category": "So", "key": "231F", "mappings": {"default": {"default": "angolo in basso a destra"}}}, {"category": "Sm", "key": "2320", "mappings": {"default": {"default": "mezzo integrale alto"}}}, {"category": "Sm", "key": "2321", "mappings": {"default": {"default": "mezzo intgrale basso"}}}, {"category": "Ps", "key": "2329", "mappings": {"default": {"default": "parentesi angolare sinistra"}}}, {"category": "Pe", "key": "232A", "mappings": {"default": {"default": "parentesi angolare a destra"}}}, {"category": "Sm", "key": "239B", "mappings": {"default": {"default": "parentesi a gancio superiore sinistro"}}}, {"category": "Sm", "key": "239C", "mappings": {"default": {"default": "estensione di parentesi sinistra"}}}, {"category": "Sm", "key": "239D", "mappings": {"default": {"default": "parentesi a gancio inferiore sinistro"}}}, {"category": "Sm", "key": "239E", "mappings": {"default": {"default": "parentesi a gancio superiore destro"}}}, {"category": "Sm", "key": "239F", "mappings": {"default": {"default": "estensione parentesi destra"}}}, {"category": "Sm", "key": "23A0", "mappings": {"default": {"default": "parentesi a gancio inferiore destro"}}}, {"category": "Sm", "key": "23A1", "mappings": {"default": {"default": "angolo superiore di parentesi quadra sinistra"}}}, {"category": "Sm", "key": "23A2", "mappings": {"default": {"default": "estensione per parentesi quadra sinistra"}}}, {"category": "Sm", "key": "23A3", "mappings": {"default": {"default": "angolo inferiore di parentesi quadra sinistra"}}}, {"category": "Sm", "key": "23A4", "mappings": {"default": {"default": "angolo superiore di parentesi quadra destra"}}}, {"category": "Sm", "key": "23A5", "mappings": {"default": {"default": "estensione per parentesi quadra destra"}}}, {"category": "Sm", "key": "23A6", "mappings": {"default": {"default": "angolo inferiore di parentesi quadra destra"}}}, {"category": "Sm", "key": "23A7", "mappings": {"default": {"default": "gancio superiore di parentesi sinistra"}}}, {"category": "Sm", "key": "23A8", "mappings": {"default": {"default": "parte centrale della parentesi graffa sinistra"}}}, {"category": "Sm", "key": "23A9", "mappings": {"default": {"default": "gancio inferiore di parentesi sinistra"}}}, {"category": "Sm", "key": "23AA", "mappings": {"default": {"default": "estensione di parentesi graffa"}}}, {"category": "Sm", "key": "23AB", "mappings": {"default": {"default": "gancio superiore di parentesi destra"}}}, {"category": "Sm", "key": "23AC", "mappings": {"default": {"default": "parte centrale della parentesi graffa destra"}}}, {"category": "Sm", "key": "23AD", "mappings": {"default": {"default": "gancio inferiore di parentesi destra"}}}, {"category": "Sm", "key": "23AE", "mappings": {"default": {"default": "estensione integrale"}}}, {"category": "Sm", "key": "23AF", "mappings": {"default": {"default": "estensione della linea orizzontale"}}}, {"category": "Sm", "key": "23B0", "mappings": {"default": {"default": "sezione di parentesi graffa superiore sinistra e destra inferiore"}}}, {"category": "Sm", "key": "23B1", "mappings": {"default": {"default": "sezione di parentesi graffa superiore destra e sinistra inferiore"}}}, {"category": "Sm", "key": "23B2", "mappings": {"default": {"default": "sommatoria superiore"}}}, {"category": "Sm", "key": "23B3", "mappings": {"default": {"default": "sommatoria inferiore"}}}, {"category": "So", "key": "23B4", "mappings": {"default": {"default": "parentesi quadra superiore"}}}, {"category": "So", "key": "23B5", "mappings": {"default": {"default": "parentesi quadra inferiore"}}}, {"category": "So", "key": "23B6", "mappings": {"default": {"default": "parentesi quadra inferiore su parentesi quadrata superiore"}}}, {"category": "So", "key": "23B7", "mappings": {"default": {"default": "simbolo radicale in basso"}}}, {"category": "So", "key": "23B8", "mappings": {"default": {"default": "riga verticale sinistra"}}}, {"category": "So", "key": "23B9", "mappings": {"default": {"default": "riga verticale destra"}}}, {"category": "Sm", "key": "23DC", "mappings": {"default": {"default": "parentesi superiore"}}}, {"category": "Sm", "key": "23DD", "mappings": {"default": {"default": "parentesi inferiore"}}}, {"category": "Sm", "key": "23DE", "mappings": {"default": {"default": "parentesi graffa superiore"}}}, {"category": "Sm", "key": "23DF", "mappings": {"default": {"default": "parentesi graffa inferiore"}}}, {"category": "Sm", "key": "23E0", "mappings": {"default": {"default": "parentesi a conchiglia superiore"}}}, {"category": "Sm", "key": "23E1", "mappings": {"default": {"default": "parentesi a conchiglia inferiore"}}}, {"category": "Ps", "key": "2768", "mappings": {"default": {"default": "ornamento di parentesi tonda sinistra medio"}}}, {"category": "Pe", "key": "2769", "mappings": {"default": {"default": "ornamento di parentesi tonda destra medio"}}}, {"category": "Ps", "key": "276A", "mappings": {"default": {"default": "ornamento di parentesi sinistra media appiattita"}}}, {"category": "Pe", "key": "276B", "mappings": {"default": {"default": "ornamento di parentesi destra media appiattita"}}}, {"category": "Ps", "key": "276C", "mappings": {"default": {"default": "ornamento di parentesi angolare sinistra media"}}}, {"category": "Pe", "key": "276D", "mappings": {"default": {"default": "ornamento di parentesi angolare destra media"}}}, {"category": "Ps", "key": "276E", "mappings": {"default": {"default": "ornamento di parentesi angolare sinistra grossa"}}}, {"category": "Pe", "key": "276F", "mappings": {"default": {"default": "ornamento di parentesi angolare destra grossa"}}}, {"category": "Ps", "key": "2770", "mappings": {"default": {"default": "ornamento di parentesi angolare sinistra grosso"}}}, {"category": "Pe", "key": "2771", "mappings": {"default": {"default": "ornamento di parentesi angolare destra grosso"}}}, {"category": "Ps", "key": "2772", "mappings": {"default": {"default": "ornamento di parentesi a conchiglia sinistro sottile"}}}, {"category": "Pe", "key": "2773", "mappings": {"default": {"default": "ornamento di parentesi a conchiglia destro sottile"}}}, {"category": "Ps", "key": "2774", "mappings": {"default": {"default": "ornamento di parentesi graffa sinistra medio"}}}, {"category": "Pe", "key": "2775", "mappings": {"default": {"default": "ornamento di parentesi graffa destra medio"}}}, {"category": "Ps", "key": "27C5", "mappings": {"default": {"default": "delimitatore bag sinistro a forma di S"}}}, {"category": "Pe", "key": "27C6", "mappings": {"default": {"default": "delimitatore bag destro a forma di S"}}}, {"category": "Ps", "key": "27E6", "mappings": {"default": {"default": "parentesi quadra sinistra bianca"}}}, {"category": "Pe", "key": "27E7", "mappings": {"default": {"default": "parentesi quadra destra bianca"}}}, {"category": "Ps", "key": "27E8", "mappings": {"default": {"default": "parentesi angolata sinistra"}}}, {"category": "Pe", "key": "27E9", "mappings": {"default": {"default": "parentesi angolata destra"}}}, {"category": "Ps", "key": "27EA", "mappings": {"default": {"default": "parentesi angolata sinistra doppia"}}}, {"category": "Pe", "key": "27EB", "mappings": {"default": {"default": "parentesi angolata destra doppia"}}}, {"category": "Ps", "key": "27EC", "mappings": {"default": {"default": "parentesi a conchiglia sinistra bianca"}}}, {"category": "Pe", "key": "27ED", "mappings": {"default": {"default": "parentesi a conchiglia destra bianca"}}}, {"category": "Ps", "key": "27EE", "mappings": {"default": {"default": "parentesi sinistra appiattita"}}}, {"category": "Pe", "key": "27EF", "mappings": {"default": {"default": "parentesi destra appiattita"}}}, {"category": "Ps", "key": "2983", "mappings": {"default": {"default": "parentesi graffa bianca sinistra"}}}, {"category": "Pe", "key": "2984", "mappings": {"default": {"default": "parentesi graffa bianca destra"}}}, {"category": "Ps", "key": "2985", "mappings": {"default": {"default": "parentesi tonda bianca sinistra"}}}, {"category": "Pe", "key": "2986", "mappings": {"default": {"default": "parentesi tonda bianca destra"}}}, {"category": "Ps", "key": "2987", "mappings": {"default": {"default": "parentesi immagine sinistra notazione Z"}}}, {"category": "Pe", "key": "2988", "mappings": {"default": {"default": "parentesi immagine destra di notazione Z"}}}, {"category": "Ps", "key": "2989", "mappings": {"default": {"default": "parentesi di rilegatura sinistra con notazione Z"}}}, {"category": "Pe", "key": "298A", "mappings": {"default": {"default": "parentesi di rilegatura destra a notazione Z"}}}, {"category": "Ps", "key": "298B", "mappings": {"default": {"default": "parentesi quadrata sinistra con sottobarra"}}}, {"category": "Pe", "key": "298C", "mappings": {"default": {"default": "parentesi quadra destra con sottobarra"}}}, {"category": "Ps", "key": "298D", "mappings": {"default": {"default": "parentesi quadrata sinistra con segno di spunta nell'angolo superiore"}}}, {"category": "Pe", "key": "298E", "mappings": {"default": {"default": "parentesi quadrata destra con segno di spunta nell'angolo inferiore"}}}, {"category": "Ps", "key": "298F", "mappings": {"default": {"default": "parentesi quadrata sinistra con segno di spunta nell'angolo inferiore"}}}, {"category": "Pe", "key": "2990", "mappings": {"default": {"default": "parentesi quadrata destra con segno di spunta nell'angolo superiore"}}}, {"category": "Ps", "key": "2991", "mappings": {"default": {"default": "parentesi angolata sinistra con punto"}}}, {"category": "Pe", "key": "2992", "mappings": {"default": {"default": "parentesi angolata destra con punto"}}}, {"category": "Ps", "key": "2993", "mappings": {"default": {"default": "parentesi sinistra ad arco con segno di minore"}}}, {"category": "Pe", "key": "2994", "mappings": {"default": {"default": "parentesi destra ad arco con segno di maggiore"}}}, {"category": "Ps", "key": "2995", "mappings": {"default": {"default": "parentesi sinistra a doppio arco con segno di minore"}}}, {"category": "Pe", "key": "2996", "mappings": {"default": {"default": "parentesi destra a doppio arco con segno di maggiore"}}}, {"category": "Ps", "key": "2997", "mappings": {"default": {"default": "parentesi a conchiglia nera sinistra"}}}, {"category": "Pe", "key": "2998", "mappings": {"default": {"default": "parentesi a conchiglia nera destra"}}}, {"category": "Ps", "key": "29D8", "mappings": {"default": {"default": "delimitatore a zig-zag sinistro"}}}, {"category": "Pe", "key": "29D9", "mappings": {"default": {"default": "delimitatore a zig-zag destro"}}}, {"category": "Ps", "key": "29DA", "mappings": {"default": {"default": "delimitatore doppio a zig-zag sinistro"}}}, {"category": "Pe", "key": "29DB", "mappings": {"default": {"default": "delimitatore doppio a zig-zag destro"}}}, {"category": "Ps", "key": "29FC", "mappings": {"default": {"default": "parentesi angolare curva a sinistra"}}}, {"category": "Pe", "key": "29FD", "mappings": {"default": {"default": "parentesi angolare curva a destra"}}}, {"category": "Ps", "key": "2E22", "mappings": {"default": {"default": "mezza parentesi superiore sinistra"}}}, {"category": "Pe", "key": "2E23", "mappings": {"default": {"default": "mezza parentesi superiore destra"}}}, {"category": "Ps", "key": "2E24", "mappings": {"default": {"default": "mezza parentesi inferiore sinistra"}}}, {"category": "Pe", "key": "2E25", "mappings": {"default": {"default": "mezza parentesi inferiore destra"}}}, {"category": "Ps", "key": "2E26", "mappings": {"default": {"default": "parentesi a U sdraiata sinistra"}}}, {"category": "Pe", "key": "2E27", "mappings": {"default": {"default": "parentesi a U sdraiata destra"}}}, {"category": "Ps", "key": "2E28", "mappings": {"default": {"default": "doppia parentesi aperta"}}}, {"category": "Pe", "key": "2E29", "mappings": {"default": {"default": "doppia parentesi chiusa"}}}, {"category": "Ps", "key": "3008", "mappings": {"default": {"default": "parentesi angolata sinistra"}}}, {"category": "Pe", "key": "3009", "mappings": {"default": {"default": "parentesi angolata destra "}}}, {"category": "Ps", "key": "300A", "mappings": {"default": {"default": "parentesi a doppio angolo sinistra"}}}, {"category": "Pe", "key": "300B", "mappings": {"default": {"default": "parentesi a doppio angolo destra"}}}, {"category": "Ps", "key": "300C", "mappings": {"default": {"default": "parentesi angolo superiore sinistro"}}}, {"category": "Pe", "key": "300D", "mappings": {"default": {"default": "parentesi angolo superiore destro"}}}, {"category": "Ps", "key": "300E", "mappings": {"default": {"default": "parentesi angolo superiore sinistro bianca"}}}, {"category": "Pe", "key": "300F", "mappings": {"default": {"default": "parentesi angolo superiore destro bianca"}}}, {"category": "Ps", "key": "3010", "mappings": {"default": {"default": "parentesi lenticolare nera sinistra"}}}, {"category": "Pe", "key": "3011", "mappings": {"default": {"default": "parentesi lenticolare nera destra"}}}, {"category": "Ps", "key": "3014", "mappings": {"default": {"default": "parentesi sinistra a conchiglia"}}}, {"category": "Pe", "key": "3015", "mappings": {"default": {"default": "parentesi destra a conchiglia"}}}, {"category": "Ps", "key": "3016", "mappings": {"default": {"default": "parentesi lenticolare bianca sinistra"}}}, {"category": "Pe", "key": "3017", "mappings": {"default": {"default": "parentesi lenticolare bianca destra"}}}, {"category": "Ps", "key": "3018", "mappings": {"default": {"default": "parentesi sinistra a conchiglia bianca"}}}, {"category": "Pe", "key": "3019", "mappings": {"default": {"default": "parentesi destra a conchiglia bianca "}}}, {"category": "Ps", "key": "301A", "mappings": {"default": {"default": "parentesi quadrata sinistra bianca"}}}, {"category": "Pe", "key": "301B", "mappings": {"default": {"default": "parentesi quadrata destra bianca"}}}, {"category": "Ps", "key": "301D", "mappings": {"default": {"default": "virgolette doppie a due punte invertite"}}}, {"category": "Pe", "key": "301E", "mappings": {"default": {"default": "doppie virgolette"}}}, {"category": "Pe", "key": "301F", "mappings": {"default": {"default": "do<PERSON><PERSON> virgolette basse"}}}, {"category": "Ps", "key": "FD3E", "mappings": {"default": {"default": "parentesi sinistra ornata"}}}, {"category": "Pe", "key": "FD3F", "mappings": {"default": {"default": "parentesi destra ornata"}}}, {"category": "Ps", "key": "FE17", "mappings": {"default": {"default": "parentesi lenticolare verticale sopra bianca"}}}, {"category": "Pe", "key": "FE18", "mappings": {"default": {"default": "parentesi lenticolare verticale sotto bianca"}}}, {"key": "FE35", "mappings": {"default": {"default": "parentesi sopra"}}, "category": "Ps"}, {"key": "FE36", "mappings": {"default": {"default": "<PERSON><PERSON> sotto"}}, "category": "Pe"}, {"key": "FE37", "mappings": {"default": {"default": "parentesi graffa sopra"}}, "category": "Ps"}, {"key": "FE38", "mappings": {"default": {"default": "parentesi graffa sotto"}}, "category": "Pe"}, {"category": "Ps", "key": "FE39", "mappings": {"default": {"default": "parentesi a conchiglia sopra"}}}, {"category": "Pe", "key": "FE3A", "mappings": {"default": {"default": "parentesi a conchiglia sotto"}}}, {"category": "Ps", "key": "FE3B", "mappings": {"default": {"default": "parentesi lenticolare verticale sopra nera"}}}, {"category": "Pe", "key": "FE3C", "mappings": {"default": {"default": "parentesi lenticolare verticale sotto nera"}}}, {"category": "Ps", "key": "FE3D", "mappings": {"default": {"default": "parentesi a doppio angolo sopra"}}}, {"category": "Pe", "key": "FE3E", "mappings": {"default": {"default": "parentesi a doppio angolo sotto"}}}, {"key": "FE3F", "mappings": {"default": {"default": "parentesi angolata sopra"}}, "category": "Ps"}, {"key": "FE40", "mappings": {"default": {"default": "parentesi angolata sotto"}}, "category": "Pe"}, {"category": "Ps", "key": "FE41", "mappings": {"default": {"default": "parentesi verticale ad angolo retto destro"}}}, {"category": "Pe", "key": "FE42", "mappings": {"default": {"default": "parentesi verticale ad angolo retto sinistro"}}}, {"category": "Ps", "key": "FE43", "mappings": {"default": {"default": "parentesi verticale ad angolo retto destro bianca"}}}, {"category": "Pe", "key": "FE44", "mappings": {"default": {"default": "parentesi verticale ad angolo retto sinistro bianca"}}}, {"category": "Ps", "key": "FE47", "mappings": {"default": {"default": "parentesi quadra sopra"}}}, {"category": "Pe", "key": "FE48", "mappings": {"default": {"default": "parentesi quadra sotto"}}}, {"category": "Ps", "key": "FE59", "mappings": {"default": {"default": "parentesi sinistra piccola"}}}, {"category": "Pe", "key": "FE5A", "mappings": {"default": {"default": "parentesi destra piccola"}}}, {"category": "Ps", "key": "FE5B", "mappings": {"default": {"default": "parentesi graffa sinistra piccola"}}}, {"category": "Pe", "key": "FE5C", "mappings": {"default": {"default": "parentesi graffa destra piccola"}}}, {"category": "Ps", "key": "FE5D", "mappings": {"default": {"default": "parentesi a conchiglia sinistra piccola"}}}, {"category": "Pe", "key": "FE5E", "mappings": {"default": {"default": "parentesi a conchiglia destra piccola"}}}, {"category": "Ps", "key": "FF08", "mappings": {"default": {"default": "parentesi sinistra a larghezza intera"}}}, {"category": "Pe", "key": "FF09", "mappings": {"default": {"default": "parentesi destra a larghezza intera"}}}, {"category": "Ps", "key": "FF3B", "mappings": {"default": {"default": "parentesi quadrata sinistra a larghezza intera"}}}, {"category": "Pe", "key": "FF3D", "mappings": {"default": {"default": "parentesi quadrata destra a larghezza intera"}}}, {"category": "Ps", "key": "FF5B", "mappings": {"default": {"default": "parentesi graffa sinistra a larghezza intera"}}}, {"category": "Pe", "key": "FF5D", "mappings": {"default": {"default": "parentesi graffa destra a larghezza intera"}}}, {"category": "Ps", "key": "FF5F", "mappings": {"default": {"default": "parentesi bianca sinistra a larghezza intera"}}}, {"category": "Pe", "key": "FF60", "mappings": {"default": {"default": "parentesi bianca destra a larghezza intera"}}}, {"category": "Ps", "key": "FF62", "mappings": {"default": {"default": "parentesi angolo superiore sinistro a mezza larghezza"}}}, {"category": "Pe", "key": "FF63", "mappings": {"default": {"default": "parentesi angolo superiore destro a mezza larghezza"}}}], "it/symbols/math_geometry.min": [{"locale": "it"}, {"category": "So", "key": "2500", "mappings": {"default": {"default": "box Drawings Light Horizontal"}}}, {"category": "So", "key": "2501", "mappings": {"default": {"default": "box Drawings Heavy Horizontal"}}}, {"category": "So", "key": "2502", "mappings": {"default": {"default": "cassetto verticale leggero"}}}, {"category": "So", "key": "2503", "mappings": {"default": {"default": "box Drawings Heavy Vertical"}}}, {"category": "So", "key": "2504", "mappings": {"default": {"default": "box Drawings Light Triple Dash Horizontal"}}}, {"category": "So", "key": "2505", "mappings": {"default": {"default": "box Drawings Heavy Triple Dash Horizontal"}}}, {"category": "So", "key": "2506", "mappings": {"default": {"default": "box Drawings Light Triple Dash Vertical"}}}, {"category": "So", "key": "2507", "mappings": {"default": {"default": "box Drawings Heavy Triple Dash Vertical"}}}, {"category": "So", "key": "2508", "mappings": {"default": {"default": "box Drawings Light Quadruple Dash Horizontal"}}}, {"category": "So", "key": "2509", "mappings": {"default": {"default": "disegni a riquadri Quadrato pesante Dash orizzontale"}}}, {"category": "So", "key": "250A", "mappings": {"default": {"default": "box Drawings Light Quadruple Dash Vertical"}}}, {"category": "So", "key": "250B", "mappings": {"default": {"default": "cassetto verticale pesante quadruplo Dash"}}}, {"category": "So", "key": "250C", "mappings": {"default": {"default": "disegni della scatola Light Down e Right"}}}, {"category": "So", "key": "250D", "mappings": {"default": {"default": "disegni della scatola Giù luce e destra pesante"}}}, {"category": "So", "key": "250E", "mappings": {"default": {"default": "box Disegna Giù Luce Pesante e Giusta"}}}, {"category": "So", "key": "250F", "mappings": {"default": {"default": "disegni scatola pesante in basso e a destra"}}}, {"category": "So", "key": "2510", "mappings": {"default": {"default": "disegni della scatola Light Down and Left"}}}, {"category": "So", "key": "2511", "mappings": {"default": {"default": "disegni scatola verso il basso luce e sinistra pesante"}}}, {"category": "So", "key": "2512", "mappings": {"default": {"default": "scatola Disegna Giù Luce Pesante e Sinistra"}}}, {"category": "So", "key": "2513", "mappings": {"default": {"default": "disegni scatola pesante in basso a sinistra"}}}, {"category": "So", "key": "2514", "mappings": {"default": {"default": "disegni della scatola Light Up and Right"}}}, {"category": "So", "key": "2515", "mappings": {"default": {"default": "box Draw Up Light and Right Heavy"}}}, {"category": "So", "key": "2516", "mappings": {"default": {"default": "box Draw Up Heavy e Right Light"}}}, {"category": "So", "key": "2517", "mappings": {"default": {"default": "disegni scatola pesante e destra"}}}, {"category": "So", "key": "2518", "mappings": {"default": {"default": "i disegni della scatola si illuminano a sinistra"}}}, {"category": "So", "key": "2519", "mappings": {"default": {"default": "box Disegna Light and Left Heavy"}}}, {"category": "So", "key": "251A", "mappings": {"default": {"default": "scatola Disegna Luce Pesante e Sinistra"}}}, {"category": "So", "key": "251B", "mappings": {"default": {"default": "disegni scatola pesante su e sinistra"}}}, {"category": "So", "key": "251C", "mappings": {"default": {"default": "box Drawings Light Vertical e Right"}}}, {"category": "So", "key": "251D", "mappings": {"default": {"default": "scatola di disegni di luce verticale e destra pesante"}}}, {"category": "So", "key": "251E", "mappings": {"default": {"default": "box Draw Up Heavy e Right Down Light"}}}, {"category": "So", "key": "251F", "mappings": {"default": {"default": "disegni scatola verso il basso Luce pesante e destra"}}}, {"category": "So", "key": "2520", "mappings": {"default": {"default": "scatola di disegni verticali pesanti e giusta luce"}}}, {"category": "So", "key": "2521", "mappings": {"default": {"default": "disegni della scatola Giù luce e destra sopra pesante"}}}, {"category": "So", "key": "2522", "mappings": {"default": {"default": "scatola in alto e in basso"}}}, {"category": "So", "key": "2523", "mappings": {"default": {"default": "box Drawings Heavy Vertical e Right"}}}, {"category": "So", "key": "2524", "mappings": {"default": {"default": "box Drawings Light Vertical and Left"}}}, {"category": "So", "key": "2525", "mappings": {"default": {"default": "scatola di disegni di luce verticale e sinistra pesante"}}}, {"category": "So", "key": "2526", "mappings": {"default": {"default": "box Draw Up Heavy e Left Down Light"}}}, {"category": "So", "key": "2527", "mappings": {"default": {"default": "disegni scatola verso il basso Luce pesante e sinistra"}}}, {"category": "So", "key": "2528", "mappings": {"default": {"default": "box Disegni verticali pesanti e luce sinistra"}}}, {"category": "So", "key": "2529", "mappings": {"default": {"default": "disegni della scatola Giù luce e sinistra su pesante"}}}, {"category": "So", "key": "252A", "mappings": {"default": {"default": "box Disegna Su e Giù <PERSON>"}}}, {"category": "So", "key": "252B", "mappings": {"default": {"default": "box Drawings Heavy Vertical e Left"}}}, {"category": "So", "key": "252C", "mappings": {"default": {"default": "disegni della scatola Light Down e Horizontal"}}}, {"category": "So", "key": "252D", "mappings": {"default": {"default": "box Drawings Left Heavy e Right Down Light"}}}, {"category": "So", "key": "252E", "mappings": {"default": {"default": "box Drawings Right Heavy e Left Down Light"}}}, {"category": "So", "key": "252F", "mappings": {"default": {"default": "disegni della scatola Giù leggero e orizzontale pesante"}}}, {"category": "So", "key": "2530", "mappings": {"default": {"default": "scatola Disegna Giù Luce Pesante e Orizzontale"}}}, {"category": "So", "key": "2531", "mappings": {"default": {"default": "disegni scatola destra e sinistra in basso pesante"}}}, {"category": "So", "key": "2532", "mappings": {"default": {"default": "disegni casella sinistra e destra verso il basso pesante"}}}, {"category": "So", "key": "2533", "mappings": {"default": {"default": "disegni scatola pesante in basso e orizzontale"}}}, {"category": "So", "key": "2534", "mappings": {"default": {"default": "disegni scatola illuminati e orizzontali"}}}, {"category": "So", "key": "2535", "mappings": {"default": {"default": "box Drawings Left Heavy e Right Up Light"}}}, {"category": "So", "key": "2536", "mappings": {"default": {"default": "box Drawings Right Heavy e Left Up Light"}}}, {"category": "So", "key": "2537", "mappings": {"default": {"default": "box Draw Up Light e Horizontal Heavy"}}}, {"category": "So", "key": "2538", "mappings": {"default": {"default": "scatola Disegna Luce Pesante e Orizzontale"}}}, {"category": "So", "key": "2539", "mappings": {"default": {"default": "scatola di disegni a destra e sinistra su pesante"}}}, {"category": "So", "key": "253A", "mappings": {"default": {"default": "box Drawings Left Light e Right Up Heavy"}}}, {"category": "So", "key": "253B", "mappings": {"default": {"default": "disegni scatola pesante e orizzontale"}}}, {"category": "So", "key": "253C", "mappings": {"default": {"default": "box Drawings Light verticale e orizzontale"}}}, {"category": "So", "key": "253D", "mappings": {"default": {"default": "scatola di disegni sinistra Luce verticale pesante e destra"}}}, {"category": "So", "key": "253E", "mappings": {"default": {"default": "cassetto a destra Luce verticale pesante e sinistra"}}}, {"category": "So", "key": "253F", "mappings": {"default": {"default": "scatola di disegni verticali leggeri e orizzontali pesanti"}}}, {"category": "So", "key": "2540", "mappings": {"default": {"default": "box Disegna la luce orizzontale pesante e discendente"}}}, {"category": "So", "key": "2541", "mappings": {"default": {"default": "box Drawings Down Heavy e Up Horizontal Light"}}}, {"category": "So", "key": "2542", "mappings": {"default": {"default": "scatola di disegni verticali pesanti e luce orizzontale"}}}, {"category": "So", "key": "2543", "mappings": {"default": {"default": "box Drawings Left Up Heavy e Right Down Light"}}}, {"category": "So", "key": "2544", "mappings": {"default": {"default": "disegni di riquadri in alto a destra e in basso a sinistra"}}}, {"category": "So", "key": "2545", "mappings": {"default": {"default": "disegni casella sinistra verso il basso Luce pesante e destra"}}}, {"category": "So", "key": "2546", "mappings": {"default": {"default": "box Drawings Right Down Heavy e Left Up Light"}}}, {"category": "So", "key": "2547", "mappings": {"default": {"default": "scatola di disegni in basso e in alto orizzontale pesante"}}}, {"category": "So", "key": "2548", "mappings": {"default": {"default": "scatola Disegna Su e Giù Pesante Orizzontale"}}}, {"category": "So", "key": "2549", "mappings": {"default": {"default": "<PERSON><PERSON>tto Disegni Luce Destra e Pesante Verticale Sinistra"}}}, {"category": "So", "key": "254A", "mappings": {"default": {"default": "cassetto a sinistra e destra verticale pesante"}}}, {"category": "So", "key": "254B", "mappings": {"default": {"default": "box Drawings Heavy Vertical e Horizontal"}}}, {"category": "So", "key": "254C", "mappings": {"default": {"default": "box Drawings Light Double Dash Horizontal"}}}, {"category": "So", "key": "254D", "mappings": {"default": {"default": "box Drawings Heavy Double Dash Horizontal"}}}, {"category": "So", "key": "254E", "mappings": {"default": {"default": "box Drawings Light Double Dash Vertical"}}}, {"category": "So", "key": "254F", "mappings": {"default": {"default": "box Drawings Heavy Double Dash Vertical"}}}, {"category": "So", "key": "2550", "mappings": {"default": {"default": "box Drawings Double Horizontal"}}}, {"category": "So", "key": "2551", "mappings": {"default": {"default": "box Drawings Double Vertical"}}}, {"category": "So", "key": "2552", "mappings": {"default": {"default": "box Disegni Giù Doppio Singolo e Destro"}}}, {"category": "So", "key": "2553", "mappings": {"default": {"default": "box Disegni Giù Doppio e Destro Singolo"}}}, {"category": "So", "key": "2554", "mappings": {"default": {"default": "disegni della casella Double Down e Right"}}}, {"category": "So", "key": "2555", "mappings": {"default": {"default": "disegni della casella in basso a destra ea sinistra"}}}, {"category": "So", "key": "2556", "mappings": {"default": {"default": "disegni casella giù doppio e singolo sinistro"}}}, {"category": "So", "key": "2557", "mappings": {"default": {"default": "box Disegni Double Down and Left"}}}, {"category": "So", "key": "2558", "mappings": {"default": {"default": "riquadro Disegni singoli e doppi"}}}, {"category": "So", "key": "2559", "mappings": {"default": {"default": "box Drawings Up Double and Right Single"}}}, {"category": "So", "key": "255A", "mappings": {"default": {"default": "box Drawings Double Up e Right"}}}, {"category": "So", "key": "255B", "mappings": {"default": {"default": "riquadro Disegni singoli e doppio sinistro"}}}, {"category": "So", "key": "255C", "mappings": {"default": {"default": "box Drawings Up Double and Left Single"}}}, {"category": "So", "key": "255D", "mappings": {"default": {"default": "box Disegni Double Up e Left"}}}, {"category": "So", "key": "255E", "mappings": {"default": {"default": "box Drawings Vertical Single and Right Double"}}}, {"category": "So", "key": "255F", "mappings": {"default": {"default": "box Drawings Vertical Double and Right Single"}}}, {"category": "So", "key": "2560", "mappings": {"default": {"default": "box Disegni Doppio verticale e destro"}}}, {"category": "So", "key": "2561", "mappings": {"default": {"default": "box Disegni singoli verticali e doppio sinistro"}}}, {"category": "So", "key": "2562", "mappings": {"default": {"default": "box Disegni Verticale doppio e singolo sinistro"}}}, {"category": "So", "key": "2563", "mappings": {"default": {"default": "box Disegni Doppio verticale e sinistro"}}}, {"category": "So", "key": "2564", "mappings": {"default": {"default": "box Disegni Giù Singolo e Doppio orizzontale"}}}, {"category": "So", "key": "2565", "mappings": {"default": {"default": "box Disegni Giù Do<PERSON>io e Singolo orizzontale"}}}, {"category": "So", "key": "2566", "mappings": {"default": {"default": "box Disegni Double Down e Horizontal"}}}, {"category": "So", "key": "2567", "mappings": {"default": {"default": "box Drawings Up Single and Horizontal Double"}}}, {"category": "So", "key": "2568", "mappings": {"default": {"default": "box Drawings Up Double and Horizontal Single"}}}, {"category": "So", "key": "2569", "mappings": {"default": {"default": "box Drawings Double Up e Horizontal"}}}, {"category": "So", "key": "256A", "mappings": {"default": {"default": "box Disegni verticali singolo e orizzontale doppio"}}}, {"category": "So", "key": "256B", "mappings": {"default": {"default": "box Drawings Vertical Double and Horizontal Single"}}}, {"category": "So", "key": "256C", "mappings": {"default": {"default": "box Disegni doppio verticale e orizzontale"}}}, {"category": "So", "key": "256D", "mappings": {"default": {"default": "box Drawings Light Arc Down e Right"}}}, {"category": "So", "key": "256E", "mappings": {"default": {"default": "box Drawings Light Arc Down and Left"}}}, {"category": "So", "key": "256F", "mappings": {"default": {"default": "box Drawings Light Arc Up and Left"}}}, {"category": "So", "key": "2570", "mappings": {"default": {"default": "box Drawings Light Arc Up e Right"}}}, {"category": "So", "key": "2571", "mappings": {"default": {"default": "disegni della scatola Luce diagonale in alto a destra in basso a sinistra"}}}, {"category": "So", "key": "2572", "mappings": {"default": {"default": "box Disegni Luce diagonale Superiore sinistra a destra inferiore"}}}, {"category": "So", "key": "2573", "mappings": {"default": {"default": "croce di disegni a croce diagonale"}}}, {"category": "So", "key": "2574", "mappings": {"default": {"default": "box Drawings Light Left"}}}, {"category": "So", "key": "2575", "mappings": {"default": {"default": "box Drawings Light Up"}}}, {"category": "So", "key": "2576", "mappings": {"default": {"default": "box Drawings Light Right"}}}, {"category": "So", "key": "2577", "mappings": {"default": {"default": "box Drawings Light Down"}}}, {"category": "So", "key": "2578", "mappings": {"default": {"default": "box Drawings Heavy Left"}}}, {"category": "So", "key": "2579", "mappings": {"default": {"default": "box Drawings Heavy Up"}}}, {"category": "So", "key": "257A", "mappings": {"default": {"default": "box Drawings Heavy Right"}}}, {"category": "So", "key": "257B", "mappings": {"default": {"default": "box Drawings Heavy Down"}}}, {"category": "So", "key": "257C", "mappings": {"default": {"default": "disegni scatola sinistra sinistra e destra pesante"}}}, {"category": "So", "key": "257D", "mappings": {"default": {"default": "disegni scatola illuminati e pesanti"}}}, {"category": "So", "key": "257E", "mappings": {"default": {"default": "box Drawings Heavy Left e Light Right"}}}, {"category": "So", "key": "257F", "mappings": {"default": {"default": "box Drawings Heavy Up e Light Down"}}}, {"category": "So", "key": "2580", "mappings": {"default": {"default": "mezzo blocco superiore"}}}, {"category": "So", "key": "2581", "mappings": {"default": {"default": "lower One Eighth Block"}}}, {"category": "So", "key": "2582", "mappings": {"default": {"default": "lower One Quarter Block"}}}, {"category": "So", "key": "2583", "mappings": {"default": {"default": "lower Three Eighths Block"}}}, {"category": "So", "key": "2584", "mappings": {"default": {"default": "lower Half Block"}}}, {"category": "So", "key": "2585", "mappings": {"default": {"default": "lower Five Eighths Block"}}}, {"category": "So", "key": "2586", "mappings": {"default": {"default": "lower Three Quarters Block"}}}, {"category": "So", "key": "2587", "mappings": {"default": {"default": "lower Seven Eighths Block"}}}, {"category": "So", "key": "2588", "mappings": {"default": {"default": "full Block"}}}, {"category": "So", "key": "2589", "mappings": {"default": {"default": "left Seven Eighths Block"}}}, {"category": "So", "key": "258A", "mappings": {"default": {"default": "left Three Quarters Block"}}}, {"category": "So", "key": "258B", "mappings": {"default": {"default": "left Five Eighths Block"}}}, {"category": "So", "key": "258C", "mappings": {"default": {"default": "left Half Block"}}}, {"category": "So", "key": "258D", "mappings": {"default": {"default": "left Three Eighths Block"}}}, {"category": "So", "key": "258E", "mappings": {"default": {"default": "left One Quarter Block"}}}, {"category": "So", "key": "258F", "mappings": {"default": {"default": "a sinistra un ottavo blocco"}}}, {"category": "So", "key": "2590", "mappings": {"default": {"default": "right Half Block"}}}, {"category": "So", "key": "2591", "mappings": {"default": {"default": "ombra leggera"}}}, {"category": "So", "key": "2592", "mappings": {"default": {"default": "ombra media"}}}, {"category": "So", "key": "2593", "mappings": {"default": {"default": "ombra scura"}}}, {"category": "So", "key": "2594", "mappings": {"default": {"default": "upper One Eighth Block"}}}, {"category": "So", "key": "2595", "mappings": {"default": {"default": "un ottavo blocco destro"}}}, {"category": "So", "key": "2596", "mappings": {"default": {"default": "quadrante in basso a sinistra"}}}, {"category": "So", "key": "2597", "mappings": {"default": {"default": "quadrante in basso a destra"}}}, {"category": "So", "key": "2598", "mappings": {"default": {"default": "quadrante in alto a sinistra"}}}, {"category": "So", "key": "2599", "mappings": {"default": {"default": "quadrante in alto a sinistra e in basso a sinistra e in basso a destra"}}}, {"category": "So", "key": "259A", "mappings": {"default": {"default": "quadrante in alto a sinistra e in basso a destra"}}}, {"category": "So", "key": "259B", "mappings": {"default": {"default": "quadrante in alto a sinistra e in alto a destra e in basso a sinistra"}}}, {"category": "So", "key": "259C", "mappings": {"default": {"default": "quadrante in alto a sinistra e in alto a destra e in basso a destra"}}}, {"category": "So", "key": "259D", "mappings": {"default": {"default": "quadrante in alto a destra"}}}, {"category": "So", "key": "259E", "mappings": {"default": {"default": "quadrante in alto a destra e in basso a sinistra"}}}, {"category": "So", "key": "259F", "mappings": {"default": {"default": "quadrante in alto a destra e in basso a sinistra e in basso a destra"}}}, {"category": "So", "key": "25A0", "mappings": {"default": {"default": "quadrato nero"}}}, {"category": "So", "key": "25A1", "mappings": {"default": {"default": "quadrato bianco"}}}, {"category": "So", "key": "25A2", "mappings": {"default": {"default": "quadrato bianco con angoli arrotondati"}}}, {"category": "So", "key": "25A3", "mappings": {"default": {"default": "quadrato bianco contenente quadratino nero"}}}, {"category": "So", "key": "25A4", "mappings": {"default": {"default": "quadrato con riempimento orizzontale"}}}, {"category": "So", "key": "25A5", "mappings": {"default": {"default": "quadrato con riempimento verticale"}}}, {"category": "So", "key": "25A6", "mappings": {"default": {"default": "quadrato con riempimento tratteggio ortogonale"}}}, {"category": "So", "key": "25A7", "mappings": {"default": {"default": "quadrato con riempimento sinistro in alto a destra inferiore"}}}, {"category": "So", "key": "25A8", "mappings": {"default": {"default": "quadrato con parte superiore destra per riempire in basso a sinistra"}}}, {"category": "So", "key": "25A9", "mappings": {"default": {"default": "quadrato con riempimento diagonale a campitura incrociata"}}}, {"category": "So", "key": "25AA", "mappings": {"default": {"default": "quadrato nero"}}}, {"category": "So", "key": "25AB", "mappings": {"default": {"default": "quadrato bianco"}}}, {"category": "So", "key": "25AC", "mappings": {"default": {"default": "retta<PERSON>lo nero"}}}, {"category": "So", "key": "25AD", "mappings": {"default": {"default": "rettangolo bianco"}}}, {"category": "So", "key": "25AE", "mappings": {"default": {"default": "rettangolo verticale nero"}}}, {"category": "So", "key": "25AF", "mappings": {"default": {"default": "rettangolo verticale bianco"}}}, {"category": "So", "key": "25B0", "mappings": {"default": {"default": "parallelogramma nero"}}}, {"category": "So", "key": "25B1", "mappings": {"default": {"default": "parallelogramma bianco"}}}, {"category": "So", "key": "25B2", "mappings": {"default": {"default": "triangolo up-pointing nero"}}}, {"category": "So", "key": "25B3", "mappings": {"default": {"default": "triangolo up-pointing bianco"}}}, {"category": "So", "key": "25B4", "mappings": {"default": {"default": "triangolo piccolo a punta nera"}}}, {"category": "So", "key": "25B5", "mappings": {"default": {"default": "piccolo triangolo bianco che punta in alto"}}}, {"category": "So", "key": "25B6", "mappings": {"default": {"default": "triangolo nero a destra"}}}, {"category": "Sm", "key": "25B7", "mappings": {"default": {"default": "triangolo bianco a destra"}}}, {"category": "So", "key": "25B8", "mappings": {"default": {"default": "triangolo nero a punta destra"}}}, {"category": "So", "key": "25B9", "mappings": {"default": {"default": "triangolo piccolo a destra bianco"}}}, {"category": "So", "key": "25BA", "mappings": {"default": {"default": "puntatore a punta destra nera"}}}, {"category": "So", "key": "25BB", "mappings": {"default": {"default": "puntatore a destra bianco"}}}, {"category": "So", "key": "25BC", "mappings": {"default": {"default": "triangolo verso il basso nero"}}}, {"category": "So", "key": "25BD", "mappings": {"default": {"default": "triangolo verso il basso bianco"}}}, {"category": "So", "key": "25BE", "mappings": {"default": {"default": "triangolo piccolo a discesa nera"}}}, {"category": "So", "key": "25BF", "mappings": {"default": {"default": "triangolo piccolo a discesa bianca"}}}, {"category": "So", "key": "25C0", "mappings": {"default": {"default": "triangolo nero a sinistra"}}}, {"category": "Sm", "key": "25C1", "mappings": {"default": {"default": "triangolo bianco a sinistra"}}}, {"category": "So", "key": "25C2", "mappings": {"default": {"default": "triangolo nero a punta sinistra"}}}, {"category": "So", "key": "25C3", "mappings": {"default": {"default": "triangolo piccolo a sinistra bianco"}}}, {"category": "So", "key": "25C4", "mappings": {"default": {"default": "puntatore a punta sinistra nero"}}}, {"category": "So", "key": "25C5", "mappings": {"default": {"default": "puntatore a sinistra bianco"}}}, {"category": "So", "key": "25C6", "mappings": {"default": {"default": "diamante nero"}}}, {"category": "So", "key": "25C7", "mappings": {"default": {"default": "diamante bianco"}}}, {"category": "So", "key": "25C8", "mappings": {"default": {"default": "diamante bianco contenente un piccolo diamante nero"}}}, {"category": "So", "key": "25C9", "mappings": {"default": {"default": "fisheye"}}}, {"category": "So", "key": "25CA", "mappings": {"default": {"default": "losanga"}}}, {"category": "So", "key": "25CB", "mappings": {"default": {"default": "cerchio bianco"}}}, {"category": "So", "key": "25CC", "mappings": {"default": {"default": "cer<PERSON>o <PERSON>"}}}, {"category": "So", "key": "25CD", "mappings": {"default": {"default": "cerchio con riempimento verticale"}}}, {"category": "So", "key": "25CE", "mappings": {"default": {"default": "bullseye"}}}, {"category": "So", "key": "25CF", "mappings": {"default": {"default": "cerchio nero"}}}, {"category": "So", "key": "25D0", "mappings": {"default": {"default": "cerchia con metà sinistra nera"}}}, {"category": "So", "key": "25D1", "mappings": {"default": {"default": "cerchia con metà nero a destra"}}}, {"category": "So", "key": "25D2", "mappings": {"default": {"default": "cerchia con metà nero inferiore"}}}, {"category": "So", "key": "25D3", "mappings": {"default": {"default": "cerchia con metà nero superiore"}}}, {"category": "So", "key": "25D4", "mappings": {"default": {"default": "cerchio con quadrante superiore destro nero"}}}, {"category": "So", "key": "25D5", "mappings": {"default": {"default": "cerchia con quadrante in bianco e nero"}}}, {"category": "So", "key": "25D6", "mappings": {"default": {"default": "sinistra Half Black Circle"}}}, {"category": "So", "key": "25D7", "mappings": {"default": {"default": "right Half Black Circle"}}}, {"category": "So", "key": "25D8", "mappings": {"default": {"default": "bullet Inverse"}}}, {"category": "So", "key": "25D9", "mappings": {"default": {"default": "cerchio bianco inverso"}}}, {"category": "So", "key": "25DA", "mappings": {"default": {"default": "cerchio bianco superiore a metà superiore"}}}, {"category": "So", "key": "25DB", "mappings": {"default": {"default": "cerchio bianco inferiore a metà inferiore"}}}, {"category": "So", "key": "25DC", "mappings": {"default": {"default": "arco circolare del quadrante in alto a sinistra"}}}, {"category": "So", "key": "25DD", "mappings": {"default": {"default": "arco circolare del quadrante superiore destro"}}}, {"category": "So", "key": "25DE", "mappings": {"default": {"default": "arco circolare quadrante inferiore destro"}}}, {"category": "So", "key": "25DF", "mappings": {"default": {"default": "arco circolare del quadrante inferiore sinistro"}}}, {"category": "So", "key": "25E0", "mappings": {"default": {"default": "mezza Cerchio Superiore"}}}, {"category": "So", "key": "25E1", "mappings": {"default": {"default": "semicerchio inferiore"}}}, {"category": "So", "key": "25E2", "mappings": {"default": {"default": "triangolo nero in basso a destra"}}}, {"category": "So", "key": "25E3", "mappings": {"default": {"default": "triangolo nero in basso a sinistra"}}}, {"category": "So", "key": "25E4", "mappings": {"default": {"default": "triangolo nero in alto a sinistra"}}}, {"category": "So", "key": "25E5", "mappings": {"default": {"default": "triangolo nero superiore destro"}}}, {"category": "So", "key": "25E6", "mappings": {"default": {"default": "white Bullet"}}}, {"category": "So", "key": "25E7", "mappings": {"default": {"default": "quadrato con mezzo nero a sinistra"}}}, {"category": "So", "key": "25E8", "mappings": {"default": {"default": "quadrato con mezzo mezzo nero"}}}, {"category": "So", "key": "25E9", "mappings": {"default": {"default": "quadrato con diagonale mezza sinistra superiore nera"}}}, {"category": "So", "key": "25EA", "mappings": {"default": {"default": "quadrato con semitono diagonale inferiore destro"}}}, {"category": "So", "key": "25EB", "mappings": {"default": {"default": "white Square con Vertical Bisecting Line"}}}, {"category": "So", "key": "25EC", "mappings": {"default": {"default": "triangolo up-pointing bianco con punto"}}}, {"category": "So", "key": "25ED", "mappings": {"default": {"default": "triangolo verso l'alto con metà sinistra nero"}}}, {"category": "So", "key": "25EE", "mappings": {"default": {"default": "triangolo verso l'alto con il mezzo nero a destra"}}}, {"category": "So", "key": "25EF", "mappings": {"default": {"default": "grande cerchio"}}}, {"category": "So", "key": "25F0", "mappings": {"default": {"default": "quadrato bianco con quadrante in alto a sinistra"}}}, {"category": "So", "key": "25F1", "mappings": {"default": {"default": "quadrato bianco con quadrante inferiore sinistro"}}}, {"category": "So", "key": "25F2", "mappings": {"default": {"default": "quadrato bianco con quadrante inferiore destro"}}}, {"category": "So", "key": "25F3", "mappings": {"default": {"default": "quadrato bianco con quadrante in alto a destra"}}}, {"category": "So", "key": "25F4", "mappings": {"default": {"default": "cerchio bianco con quadrante in alto a sinistra"}}}, {"category": "So", "key": "25F5", "mappings": {"default": {"default": "cerchio bianco con quadrante in basso a sinistra"}}}, {"category": "So", "key": "25F6", "mappings": {"default": {"default": "cerchio bianco con quadrante inferiore destro"}}}, {"category": "So", "key": "25F7", "mappings": {"default": {"default": "cerchio bianco con quadrante in alto a destra"}}}, {"category": "Sm", "key": "25F8", "mappings": {"default": {"default": "triangolo in alto a sinistra"}}}, {"category": "Sm", "key": "25F9", "mappings": {"default": {"default": "triangolo in alto a destra"}}}, {"category": "Sm", "key": "25FA", "mappings": {"default": {"default": "triangolo in basso a sinistra"}}}, {"category": "Sm", "key": "25FB", "mappings": {"default": {"default": "quadrato medio bianco"}}}, {"category": "Sm", "key": "25FC", "mappings": {"default": {"default": "quadrato nero medio"}}}, {"category": "Sm", "key": "25FD", "mappings": {"default": {"default": "quadrato piccolo medio bianco"}}}, {"category": "Sm", "key": "25FE", "mappings": {"default": {"default": "quadrato nero medio piccolo"}}}, {"category": "Sm", "key": "25FF", "mappings": {"default": {"default": "triangolo in basso a destra"}}}, {"category": "So", "key": "2B12", "mappings": {"default": {"default": "quadrato con Top Half Black"}}}, {"category": "So", "key": "2B13", "mappings": {"default": {"default": "quadrato con fondo nero mezzo"}}}, {"category": "So", "key": "2B14", "mappings": {"default": {"default": "quadrato con mezza diagonale superiore destra nera"}}}, {"category": "So", "key": "2B15", "mappings": {"default": {"default": "quadrato con semitono diagonale inferiore sinistro"}}}, {"category": "So", "key": "2B16", "mappings": {"default": {"default": "diamante con mezzo nero a sinistra"}}}, {"category": "So", "key": "2B17", "mappings": {"default": {"default": "diamante con mezzo mezzo nero"}}}, {"category": "So", "key": "2B18", "mappings": {"default": {"default": "diaman<PERSON> con Top Half Black"}}}, {"category": "So", "key": "2B19", "mappings": {"default": {"default": "diamante con fondo mezzo nero"}}}, {"category": "So", "key": "2B1A", "mappings": {"default": {"default": "piazza punt<PERSON>ta"}}}, {"category": "So", "key": "2B1B", "mappings": {"default": {"default": "grande quadrato nero"}}}, {"category": "So", "key": "2B1C", "mappings": {"default": {"default": "grande quadrato bianco"}}}, {"category": "So", "key": "2B1D", "mappings": {"default": {"default": "quadrato nero molto piccolo"}}}, {"category": "So", "key": "2B1E", "mappings": {"default": {"default": "quadrato bianco molto piccolo"}}}, {"category": "So", "key": "2B1F", "mappings": {"default": {"default": "pentagono nero"}}}, {"category": "So", "key": "2B20", "mappings": {"default": {"default": "pentagono bianco"}}}, {"category": "So", "key": "2B21", "mappings": {"default": {"default": "esagono bianco"}}}, {"category": "So", "key": "2B22", "mappings": {"default": {"default": "esagono nero"}}}, {"category": "So", "key": "2B23", "mappings": {"default": {"default": "esagono nero orizzontale"}}}, {"category": "So", "key": "2B24", "mappings": {"default": {"default": "grande cerchio nero"}}}, {"category": "So", "key": "2B25", "mappings": {"default": {"default": "diamante medio nero"}}}, {"category": "So", "key": "2B26", "mappings": {"default": {"default": "diamante medio bianco"}}}, {"category": "So", "key": "2B27", "mappings": {"default": {"default": "black Medium Losanga"}}}, {"category": "So", "key": "2B28", "mappings": {"default": {"default": "losanga medio bianco"}}}, {"category": "So", "key": "2B29", "mappings": {"default": {"default": "piccolo diamante nero"}}}, {"category": "So", "key": "2B2A", "mappings": {"default": {"default": "piccola losanga nera"}}}, {"category": "So", "key": "2B2B", "mappings": {"default": {"default": "piccola losanga bianca"}}}, {"category": "So", "key": "2B2C", "mappings": {"default": {"default": "el<PERSON><PERSON> oriz<PERSON> nera"}}}, {"category": "So", "key": "2B2D", "mappings": {"default": {"default": "ellisse orizzontale bianca"}}}, {"category": "So", "key": "2B2E", "mappings": {"default": {"default": "ellisse verticale nera"}}}, {"category": "So", "key": "2B2F", "mappings": {"default": {"default": "ellisse verticale bianca"}}}, {"category": "So", "key": "2B50", "mappings": {"default": {"default": "stella media bianca"}}}, {"category": "So", "key": "2B51", "mappings": {"default": {"default": "stella piccola nera"}}}, {"category": "So", "key": "2B52", "mappings": {"default": {"default": "piccola stella bianca"}}}, {"category": "So", "key": "2B53", "mappings": {"default": {"default": "pentagono nero a destra"}}}, {"category": "So", "key": "2B54", "mappings": {"default": {"default": "pentagono bianco a destra"}}}, {"category": "So", "key": "2B55", "mappings": {"default": {"default": "grande cerchio pesante"}}}, {"category": "So", "key": "2B56", "mappings": {"default": {"default": "ovale pesante con interno ovale"}}}, {"category": "So", "key": "2B57", "mappings": {"default": {"default": "heavy Circle con Circle Inside"}}}, {"category": "So", "key": "2B58", "mappings": {"default": {"default": "heavy Circle"}}}, {"category": "So", "key": "2B59", "mappings": {"default": {"default": "heavy Circle Saltire"}}}], "it/symbols/math_harpoons.min": [{"locale": "it"}, {"key": "21BC", "mappings": {"default": {"default": "arpione sinistro sopra"}}, "category": "So"}, {"key": "21BD", "mappings": {"default": {"default": "arpione sinistro in basso"}}, "category": "So"}, {"key": "21BE", "mappings": {"default": {"default": "arpione su a destra"}}, "category": "So"}, {"key": "21BF", "mappings": {"default": {"default": "arpione su a sinistra"}}, "category": "So"}, {"key": "21C0", "mappings": {"default": {"default": "arpione destro sopra"}}, "category": "So"}, {"key": "21C1", "mappings": {"default": {"default": "arpione destro in basso"}}, "category": "So"}, {"key": "21C2", "mappings": {"default": {"default": "arpione giù a destra"}}, "category": "So"}, {"key": "21C3", "mappings": {"default": {"default": "arpione giù a sinistra"}}, "category": "So"}, {"key": "21CB", "mappings": {"default": {"default": "arpione sinistro sopra arpione destro"}}, "category": "So"}, {"key": "21CC", "mappings": {"default": {"default": "arpione destro sopra arpione sinistro"}}, "category": "So"}, {"category": "Sm", "key": "294A", "mappings": {"default": {"default": "sinistra Barb Up Right Barb Down Harpoon"}}}, {"category": "Sm", "key": "294B", "mappings": {"default": {"default": "a sinistra Barb giù a destra Barb Up Harpoon"}}}, {"category": "Sm", "key": "294C", "mappings": {"default": {"default": "up Barb Right Down Barb Left Harpoon"}}}, {"category": "Sm", "key": "294D", "mappings": {"default": {"default": "up Barb Left Down Barb Right Harpoon"}}}, {"category": "Sm", "key": "294E", "mappings": {"default": {"default": "a sinistra Barb Up Right Barb Up Harpoon"}}}, {"key": "294F", "mappings": {"default": {"default": "arpione su e giù verso destra"}}, "category": "Sm"}, {"category": "Sm", "key": "2950", "mappings": {"default": {"default": "sinistra Barb Down Right Barb Down Harpoon"}}}, {"key": "2951", "mappings": {"default": {"default": "arpione su e giù verso sinistra"}}, "category": "Sm"}, {"category": "Sm", "key": "2952", "mappings": {"default": {"default": "arpione a sinistra con Barb Up To Bar"}}}, {"category": "Sm", "key": "2953", "mappings": {"default": {"default": "giusto arpione con Barb Up To Bar"}}}, {"category": "Sm", "key": "2954", "mappings": {"default": {"default": "upward Harpoon con Barb Right To Bar"}}}, {"category": "Sm", "key": "2955", "mappings": {"default": {"default": "downward Harpoon con Barb Right To Bar"}}}, {"category": "Sm", "key": "2956", "mappings": {"default": {"default": "arpione a sinistra con Barb Down To Bar"}}}, {"category": "Sm", "key": "2957", "mappings": {"default": {"default": "giusto arpione con Barb giù al bar"}}}, {"category": "Sm", "key": "2958", "mappings": {"default": {"default": "upward Harpoon con Barb Left To Bar"}}}, {"category": "Sm", "key": "2959", "mappings": {"default": {"default": "downward Harpoon con Barb Left To Bar"}}}, {"category": "Sm", "key": "295A", "mappings": {"default": {"default": "arpione a sinistra con Barb Up from Bar"}}}, {"category": "Sm", "key": "295B", "mappings": {"default": {"default": "g<PERSON><PERSON> con Barb Up from Bar"}}}, {"category": "Sm", "key": "295C", "mappings": {"default": {"default": "upward Harpoon with <PERSON><PERSON> Right from Bar"}}}, {"category": "Sm", "key": "295D", "mappings": {"default": {"default": "downward Harpoon with <PERSON><PERSON> Right from Bar"}}}, {"category": "Sm", "key": "295E", "mappings": {"default": {"default": "arpione a sinistra con Barb giù dal bar"}}}, {"category": "Sm", "key": "295F", "mappings": {"default": {"default": "g<PERSON><PERSON> con <PERSON><PERSON> g<PERSON><PERSON>"}}}, {"category": "Sm", "key": "2960", "mappings": {"default": {"default": "upward Harpoon with <PERSON><PERSON> Left from Bar"}}}, {"category": "Sm", "key": "2961", "mappings": {"default": {"default": "downward Harpoon with <PERSON><PERSON> Left from Bar"}}}, {"category": "Sm", "key": "2962", "mappings": {"default": {"default": "arpione a sinistra con Barb in alto a sinistra Arpione con Barb giù"}}}, {"category": "Sm", "key": "2963", "mappings": {"default": {"default": "upward Harpoon with <PERSON><PERSON> Left Beside Upward Harpoon with <PERSON><PERSON> Right"}}}, {"category": "Sm", "key": "2964", "mappings": {"default": {"default": "a destra Arpione con Barb Up Above Rightward con Barb Down"}}}, {"category": "Sm", "key": "2965", "mappings": {"default": {"default": "downward Harpoon with <PERSON><PERSON> Left Beside Downward Harpoon with <PERSON><PERSON> Right"}}}, {"category": "Sm", "key": "2966", "mappings": {"default": {"default": "arpione a sinistra con Barb Up Above Rightward con Barb Up"}}}, {"category": "Sm", "key": "2967", "mappings": {"default": {"default": "arpione a sinistra con Barb giù in alto a destra Arpione con Barb giù"}}}, {"category": "Sm", "key": "2968", "mappings": {"default": {"default": "a destra Arpione con Barb Up in alto a sinistra Harpoon con Barb Up"}}}, {"category": "Sm", "key": "2969", "mappings": {"default": {"default": "a destra Arpione con Barb giù in alto a sinistra Arpione con Barb giù"}}}, {"category": "Sm", "key": "296A", "mappings": {"default": {"default": "arpione a sinistra con Barb Up Above Long Dash"}}}, {"category": "Sm", "key": "296B", "mappings": {"default": {"default": "arpione a sinistra con Barb giù sotto Long Dash"}}}, {"category": "Sm", "key": "296C", "mappings": {"default": {"default": "giusto arpione con Barb Up Above Long Dash"}}}, {"category": "Sm", "key": "296D", "mappings": {"default": {"default": "a destra Arpione con Barb giù sotto Long Dash"}}}, {"category": "Sm", "key": "296E", "mappings": {"default": {"default": "upward Harpoon with <PERSON><PERSON> Left Beside Downward Harpoon with <PERSON><PERSON> Right"}}}, {"category": "Sm", "key": "296F", "mappings": {"default": {"default": "downward Harpoon with <PERSON><PERSON> Left Beside Upward Harpoon with <PERSON><PERSON> Right"}}}, {"category": "Sm", "key": "297C", "mappings": {"default": {"default": "coda di pesce sinistra"}}}, {"category": "Sm", "key": "297D", "mappings": {"default": {"default": "coda di pesce giusta"}}}, {"category": "Sm", "key": "297E", "mappings": {"default": {"default": "coda di pesce"}}}, {"category": "Sm", "key": "297F", "mappings": {"default": {"default": "giù coda di pesce"}}}], "it/symbols/math_non_characters.min": [{"locale": "it"}, {"category": "Ll", "key": "210F", "mappings": {"default": {"default": "planck Constant Over Two Pi"}}}, {"category": "So", "key": "2114", "mappings": {"default": {"default": "l B Bar Symbol"}}}, {"key": "2116", "mappings": {"default": {"default": "simbolo di numero"}}, "category": "So"}, {"category": "So", "key": "2117", "mappings": {"default": {"default": "copyright della registrazione del suono"}}}, {"category": "So", "key": "211E", "mappings": {"default": {"default": "presa di prescrizione"}}}, {"category": "So", "key": "211F", "mappings": {"default": {"default": "risposta"}}}, {"category": "So", "key": "2120", "mappings": {"default": {"default": "segno di servizio"}}}, {"category": "So", "key": "2121", "mappings": {"default": {"default": "segno del telefono"}}}, {"key": "2122", "mappings": {"default": {"default": "segno di treid mark"}}, "category": "So"}, {"category": "So", "key": "2123", "mappings": {"default": {"default": "versicle"}}}, {"key": "2125", "mappings": {"default": {"default": "segno di oncia"}}, "category": "So"}, {"key": "2126", "mappings": {"default": {"default": "simbolo dell'ohm"}}, "category": "<PERSON>"}, {"key": "2127", "mappings": {"default": {"default": "ohm invertito"}}, "category": "So"}, {"category": "<PERSON>", "key": "212A", "mappings": {"default": {"default": "se<PERSON>"}}}, {"key": "212B", "mappings": {"default": {"default": "angstrom"}}, "category": "<PERSON>"}, {"category": "So", "key": "212E", "mappings": {"default": {"default": "simbolo stimato"}}}, {"key": "2132", "mappings": {"default": {"default": "turned f maiuscola"}}, "category": "<PERSON>"}, {"category": "Ll", "key": "2139", "mappings": {"default": {"default": "fonte di informazione"}}}, {"category": "So", "key": "213A", "mappings": {"default": {"default": "capitale ruotato Q"}}}, {"category": "So", "key": "213B", "mappings": {"default": {"default": "segno di facsimile"}}}, {"category": "Sm", "key": "2141", "mappings": {"default": {"default": "trasformato Sans-Serif Capitale G"}}}, {"category": "Sm", "key": "2142", "mappings": {"default": {"default": "trasformato Sans-Serif Capital L"}}}, {"category": "Sm", "key": "2143", "mappings": {"default": {"default": "capitale Sans-Serif inversa L"}}}, {"category": "Sm", "key": "2144", "mappings": {"default": {"default": "svolta Sans-Serif Capital Y"}}}], "it/symbols/math_symbols.min": [{"locale": "it"}, {"key": "0021", "mappings": {"default": {"default": "punto esclamativo"}}, "category": "Po"}, {"key": "0022", "mappings": {"default": {"default": "vir<PERSON><PERSON>"}}, "category": "Po"}, {"key": "0023", "mappings": {"default": {"default": "cancelletto"}}, "category": "Po"}, {"key": "0024", "mappings": {"default": {"default": "dollaro"}}, "category": "Sc"}, {"key": "0025", "mappings": {"default": {"default": "percento"}}, "category": "Po"}, {"key": "0026", "mappings": {"default": {"default": "e commerciale"}}, "category": "Po"}, {"key": "0027", "mappings": {"default": {"default": "apostrofo"}}, "category": "Po"}, {"key": "002A", "mappings": {"default": {"default": "asterisco"}}, "category": "Po"}, {"key": "002B", "mappings": {"default": {"default": "più"}}, "category": "Sm"}, {"key": "002C", "mappings": {"default": {"default": "virgola"}}, "category": "Po"}, {"key": "002D", "mappings": {"default": {"default": "meno"}}, "category": "Pd"}, {"category": "Po", "key": "002E", "mappings": {"default": {"default": "punto"}}}, {"key": "002F", "mappings": {"default": {"default": "diviso"}}, "category": "Po"}, {"key": "003A", "mappings": {"default": {"default": "due punti"}}, "category": "Po"}, {"key": "003B", "mappings": {"default": {"default": "punto e virgola"}}, "category": "Po"}, {"key": "003C", "mappings": {"default": {"default": "minore di"}}, "category": "Sm"}, {"key": "003D", "mappings": {"default": {"default": "uguale a"}}, "category": "Sm"}, {"key": "003E", "mappings": {"default": {"default": "maggiore di"}}, "category": "Sm"}, {"key": "003F", "mappings": {"default": {"default": "punto interrogativo"}}, "category": "Po"}, {"key": "0040", "mappings": {"default": {"default": "simbolo at"}}, "category": "Po"}, {"key": "005C", "mappings": {"default": {"default": "back slash"}}, "category": "Po"}, {"key": "005E", "mappings": {"default": {"default": "accento circonflesso"}}, "category": "Sk"}, {"key": "005F", "mappings": {"default": {"default": "barra sotto"}}, "category": "Pc"}, {"key": "0060", "mappings": {"default": {"default": "accento grave"}}, "category": "Sk"}, {"key": "007C", "mappings": {"default": {"default": "barra verticale"}}, "category": "Sm"}, {"key": "007E", "mappings": {"default": {"default": "tilde"}}, "category": "Sm"}, {"key": "00A1", "mappings": {"default": {"default": "punto esclamativo rovesciato"}}, "category": "Po"}, {"key": "00A2", "mappings": {"default": {"default": "cent"}}, "category": "Sc"}, {"key": "00A3", "mappings": {"default": {"default": "simbolo sterlina"}}, "category": "Sc"}, {"key": "00A4", "mappings": {"default": {"default": "segno di valuta"}}, "category": "Sc"}, {"key": "00A5", "mappings": {"default": {"default": "simbolo yen"}}, "category": "Sc"}, {"key": "00A6", "mappings": {"default": {"default": "barra verticale interrotta"}}, "category": "So"}, {"key": "00A7", "mappings": {"default": {"default": "simbolo di riferimento a sezione"}}, "category": "Po"}, {"key": "00A8", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}, "category": "Sk"}, {"key": "00A9", "mappings": {"default": {"default": "copyright"}}, "category": "So"}, {"key": "00AA", "mappings": {"default": {"default": "indicatore di ordinale femminile"}}, "category": "Lo"}, {"key": "00AB", "mappings": {"default": {"default": "simbolo di citazione a doppio angolo verso sinistra"}}, "category": "Pi"}, {"key": "00AC", "mappings": {"default": {"default": "simbolo not"}}, "category": "Sm"}, {"key": "00AE", "mappings": {"default": {"default": "simbolo di marchio registrato"}}, "category": "So"}, {"key": "00AF", "mappings": {"default": {"default": "barra sopra"}}, "category": "Sk"}, {"key": "00B0", "mappings": {"default": {"default": "gradi"}}, "category": "So"}, {"key": "00B1", "mappings": {"default": {"default": "più o meno"}}, "category": "Sm"}, {"key": "00B4", "mappings": {"default": {"default": "accento acuto"}}, "category": "Sk"}, {"key": "00B5", "mappings": {"default": {"default": "segno di micro"}}, "category": "Ll"}, {"key": "00B6", "mappings": {"default": {"default": "segno di paragrafo"}}, "category": "Po"}, {"key": "00B7", "mappings": {"default": {"default": "puntino nel mezzo"}}, "category": "Po"}, {"key": "00B8", "mappings": {"default": {"default": "cediglia"}}, "category": "Sk"}, {"key": "00BA", "mappings": {"default": {"default": "indicatore di ordinale maschile"}}, "category": "Lo"}, {"key": "00BB", "mappings": {"default": {"default": "simbolo di citazione a doppio angolo verso destra"}}, "category": "Pf"}, {"key": "00BF", "mappings": {"default": {"default": "punto di domanda rovesciato"}}, "category": "Po"}, {"key": "00D7", "mappings": {"default": {"default": "per", "alternative": "moltiplica<PERSON>"}}, "category": "Sm"}, {"key": "00F7", "mappings": {"default": {"default": "diviso"}}, "category": "Sm"}, {"key": "02B9", "mappings": {"default": {"default": "primo"}}, "category": "Lm"}, {"key": "02BA", "mappings": {"default": {"default": "doppio primo"}}, "category": "Lm"}, {"key": "02D8", "mappings": {"default": {"default": "breve"}}, "category": "Sk"}, {"key": "02D9", "mappings": {"default": {"default": "punto sopra"}}, "category": "Sk"}, {"key": "02DA", "mappings": {"default": {"default": "anello sopra"}}, "category": "Sk"}, {"key": "02DB", "mappings": {"default": {"default": "ogonek"}}, "category": "Sk"}, {"key": "02DC", "mappings": {"default": {"default": "tilde"}}, "category": "Sk"}, {"key": "02DD", "mappings": {"default": {"default": "doppio accento acuto"}}, "category": "Sk"}, {"key": "2010", "mappings": {"default": {"default": "meno"}}, "category": "Pd"}, {"category": "Pd", "key": "2011", "mappings": {"default": {"default": "lineetta senza interruzioni"}}}, {"category": "Pd", "key": "2012", "mappings": {"default": {"default": "lineetta cifra"}}}, {"key": "2013", "mappings": {"default": {"default": "lineetta enne"}}, "category": "Pd"}, {"category": "Pd", "key": "2014", "mappings": {"default": {"default": "lineetta emme"}}}, {"key": "2015", "mappings": {"default": {"default": "lineetta di citazione"}}, "category": "Pd"}, {"key": "2016", "mappings": {"default": {"default": "doppia barra verticale"}}, "category": "Po"}, {"category": "Po", "key": "2017", "mappings": {"default": {"default": "doppia linea orizzontale bassa"}}}, {"key": "2018", "mappings": {"default": {"default": "virgol<PERSON> sinistra singola"}}, "category": "Pi"}, {"key": "2019", "mappings": {"default": {"default": "virgoletta destra singola"}}, "category": "Pf"}, {"key": "201A", "mappings": {"default": {"default": "segno di citazione singolo basso a destra"}}, "category": "Ps"}, {"category": "Pi", "key": "201B", "mappings": {"default": {"default": "virgolette singola invertita a sinistra"}}}, {"key": "201C", "mappings": {"default": {"default": "segno di citazione doppio a sinistra"}}, "category": "Pi"}, {"key": "201D", "mappings": {"default": {"default": "segno di citazione doppio a destra"}}, "category": "Pf"}, {"key": "201E", "mappings": {"default": {"default": "doppio segno di citazione basso a destra"}}, "category": "Ps"}, {"category": "Pi", "key": "201F", "mappings": {"default": {"default": "virgolette doppie invertite a sinistra"}}}, {"key": "2020", "mappings": {"default": {"default": "obelisco"}}, "category": "Po"}, {"key": "2021", "mappings": {"default": {"default": "doppio obelisco"}}, "category": "Po"}, {"key": "2022", "mappings": {"default": {"default": "punto elenco"}}, "category": "Po"}, {"category": "Po", "key": "2023", "mappings": {"default": {"default": "punto elenco triangolare"}}}, {"category": "Po", "key": "2024", "mappings": {"default": {"default": "punto"}}}, {"key": "2025", "mappings": {"default": {"default": "due punti in orizzontale"}}, "category": "Po"}, {"key": "2026", "mappings": {"default": {"default": "punti di sospensione"}}, "category": "Po"}, {"category": "Po", "key": "2027", "mappings": {"default": {"default": "punto di sillabazione"}}}, {"key": "2030", "mappings": {"default": {"default": "segno per mille"}}, "category": "Po"}, {"key": "2031", "mappings": {"default": {"default": "segno per diecimila"}}, "category": "Po"}, {"key": "2032", "mappings": {"default": {"default": "primo"}}, "category": "Po"}, {"key": "2033", "mappings": {"default": {"default": "doppio primo"}}, "category": "Po"}, {"key": "2034", "mappings": {"default": {"default": "triplo primo"}}, "category": "Po"}, {"key": "2035", "mappings": {"default": {"default": "primo inverso"}}, "category": "Po"}, {"key": "2036", "mappings": {"default": {"default": "doppio primo inverso"}}, "category": "Po"}, {"category": "Po", "key": "2037", "mappings": {"default": {"default": "triple Prime invertito"}}}, {"category": "Po", "key": "2038", "mappings": {"default": {"default": "segno di omissione"}}}, {"key": "2039", "mappings": {"default": {"default": "simbolo di citazione ad angolo singolo verso sinistra"}}, "category": "Pi"}, {"key": "203A", "mappings": {"default": {"default": "simbolo di citazione ad angolo singolo verso destra"}}, "category": "Pf"}, {"category": "Po", "key": "203B", "mappings": {"default": {"default": "segno di riferimento"}}}, {"category": "Po", "key": "203C", "mappings": {"default": {"default": "doppio punto esclamativo"}}}, {"category": "Po", "key": "203D", "mappings": {"default": {"default": "punto esclarrogativo"}}}, {"key": "203E", "mappings": {"default": {"default": "barra superiore"}}, "category": "Po"}, {"category": "Pc", "key": "203F", "mappings": {"default": {"default": "legatura invertita"}}}, {"category": "Pc", "key": "2040", "mappings": {"default": {"default": "carattere di legatura"}}}, {"category": "Po", "key": "2041", "mappings": {"default": {"default": "punto di inserimento del caret"}}}, {"category": "Po", "key": "2042", "mappings": {"default": {"default": "asterismo"}}}, {"category": "Po", "key": "2043", "mappings": {"default": {"default": "carattere elenco a trattino"}}}, {"category": "Sm", "key": "2044", "mappings": {"default": {"default": "barra di frazione"}}}, {"category": "Po", "key": "2047", "mappings": {"default": {"default": "doppio punto interrogativo"}}}, {"category": "Po", "key": "2048", "mappings": {"default": {"default": "punto interrogativo ed esclamativo"}}}, {"category": "Po", "key": "2049", "mappings": {"default": {"default": "punto esclamativo e interrogativo"}}}, {"category": "Po", "key": "204B", "mappings": {"default": {"default": "segno Pilcrow invertito"}}}, {"category": "Po", "key": "204C", "mappings": {"default": {"default": "proiettile nero verso sinistra"}}}, {"category": "Po", "key": "204D", "mappings": {"default": {"default": "proiettile nero verso destra"}}}, {"category": "Po", "key": "204E", "mappings": {"default": {"default": "as<PERSON><PERSON> basso"}}}, {"category": "Po", "key": "204F", "mappings": {"default": {"default": "punto e virgola invertito"}}}, {"category": "Po", "key": "2050", "mappings": {"default": {"default": "avvicinamento"}}}, {"category": "Po", "key": "2051", "mappings": {"default": {"default": "due asterischi allineati verticalmente"}}}, {"category": "Sm", "key": "2052", "mappings": {"default": {"default": "segno meno commerciale"}}}, {"category": "Po", "key": "2053", "mappings": {"default": {"default": "lineetta ondulata"}}}, {"category": "Pc", "key": "2054", "mappings": {"default": {"default": "carattere di legatura invertito"}}}, {"category": "Po", "key": "2055", "mappings": {"default": {"default": "punteggiatura a fiori"}}}, {"category": "Po", "key": "2056", "mappings": {"default": {"default": "punteggiatura a tre punti"}}}, {"category": "Po", "key": "2057", "mappings": {"default": {"default": "quattro volte primo"}}}, {"category": "Po", "key": "2058", "mappings": {"default": {"default": "punteggiatura a quattro punti"}}}, {"category": "Po", "key": "2059", "mappings": {"default": {"default": "punteggiatura a cinque punti"}}}, {"category": "Po", "key": "205A", "mappings": {"default": {"default": "punteggiatura a due punti"}}}, {"category": "Po", "key": "205B", "mappings": {"default": {"default": "segno di quattro punti"}}}, {"category": "Po", "key": "205C", "mappings": {"default": {"default": "croce punteggia<PERSON>"}}}, {"category": "Po", "key": "205D", "mappings": {"default": {"default": "tre punti verticali"}}}, {"category": "Po", "key": "205E", "mappings": {"default": {"default": "quattro punti verticali"}}}, {"category": "Sm", "key": "207A", "mappings": {"default": {"default": "apice più"}}}, {"category": "Sm", "key": "207B", "mappings": {"default": {"default": "apice meno"}}}, {"category": "Sm", "key": "207C", "mappings": {"default": {"default": "apice meno"}}}, {"category": "Ps", "key": "207D", "mappings": {"default": {"default": "apice parentesi aperta"}}}, {"category": "Pe", "key": "207E", "mappings": {"default": {"default": "apice parentesi chiusa"}}}, {"category": "Sm", "key": "208A", "mappings": {"default": {"default": "pedice più"}}}, {"category": "Sm", "key": "208B", "mappings": {"default": {"default": "pedice meno"}}}, {"category": "Sm", "key": "208C", "mappings": {"default": {"default": "pedice uguale"}}}, {"category": "Ps", "key": "208D", "mappings": {"default": {"default": "pedice parentesi aperta"}}}, {"category": "Pe", "key": "208E", "mappings": {"default": {"default": "pedice parentesi chiusa"}}}, {"category": "So", "key": "214A", "mappings": {"default": {"default": "linea di proprietà"}}}, {"category": "Sm", "key": "214B", "mappings": {"default": {"default": "e commerciale capovolta"}}}, {"category": "So", "key": "214C", "mappings": {"default": {"default": "per"}}}, {"category": "So", "key": "214D", "mappings": {"default": {"default": "aktieselskab"}}}, {"category": "Ll", "key": "214E", "mappings": {"default": {"default": "F maiuscola piccola capovolta"}}}, {"key": "2200", "mappings": {"default": {"default": "per ogni"}}, "category": "Sm"}, {"key": "2201", "mappings": {"default": {"default": "complemento"}}, "category": "Sm"}, {"key": "2203", "mappings": {"default": {"default": "esiste"}}, "category": "Sm"}, {"key": "2204", "mappings": {"default": {"default": "non esiste"}}, "category": "Sm"}, {"key": "2205", "mappings": {"default": {"default": "insieme vuoto"}}, "category": "Sm"}, {"key": "2206", "mappings": {"default": {"default": "incremento"}}, "category": "Sm"}, {"key": "2208", "mappings": {"default": {"default": "appartenente a"}}, "category": "Sm"}, {"key": "2209", "mappings": {"default": {"default": "non appartenente a"}}, "category": "Sm"}, {"key": "220A", "mappings": {"default": {"default": "appartenente a"}}, "category": "Sm"}, {"key": "220B", "mappings": {"default": {"default": "contiene come membro"}}, "category": "Sm"}, {"key": "220C", "mappings": {"default": {"default": "non contiene come membro"}}, "category": "Sm"}, {"key": "220D", "mappings": {"default": {"default": "contiene come membro piccolo"}}, "category": "Sm"}, {"key": "220E", "mappings": {"default": {"default": "fine dimostrazione"}}, "category": "Sm"}, {"key": "220F", "mappings": {"default": {"default": "produttoria"}}, "category": "Sm"}, {"key": "2210", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>o"}}, "category": "Sm"}, {"key": "2211", "mappings": {"default": {"default": "sommatoria"}}, "category": "Sm"}, {"key": "2212", "mappings": {"default": {"default": "meno"}}, "category": "Sm"}, {"key": "2213", "mappings": {"default": {"default": "meno più"}}, "category": "Sm"}, {"key": "2214", "mappings": {"default": {"default": "punto più"}}, "category": "Sm"}, {"key": "2215", "mappings": {"default": {"default": "barra di divisione"}}, "category": "Sm"}, {"key": "2216", "mappings": {"default": {"default": "insieme meno"}}, "category": "Sm"}, {"key": "2217", "mappings": {"default": {"default": "asterisco"}}, "category": "Sm"}, {"key": "2218", "mappings": {"default": {"default": "composizione"}}, "category": "Sm"}, {"key": "2219", "mappings": {"default": {"default": "operatore punto"}}, "category": "Sm"}, {"key": "221A", "mappings": {"default": {"default": "radice quadrata"}}, "category": "Sm"}, {"key": "221B", "mappings": {"default": {"default": "radice cubica"}}, "category": "Sm"}, {"key": "221C", "mappings": {"default": {"default": "radice quarta"}}, "category": "Sm"}, {"key": "221D", "mappings": {"default": {"default": "proporzionale a"}}, "category": "Sm"}, {"key": "221E", "mappings": {"default": {"default": "infinito"}}, "category": "Sm"}, {"key": "221F", "mappings": {"default": {"default": "angolo retto"}}, "category": "Sm"}, {"key": "2220", "mappings": {"default": {"default": "angolo"}}, "category": "Sm"}, {"key": "2221", "mappings": {"default": {"default": "angolo misurato"}}, "category": "Sm"}, {"key": "2222", "mappings": {"default": {"default": "angolo sferico"}}, "category": "Sm"}, {"category": "Sm", "key": "2223", "mappings": {"default": {"default": "divide"}}}, {"category": "Sm", "key": "2224", "mappings": {"default": {"default": "non divide"}}}, {"key": "2225", "mappings": {"default": {"default": "parallelo a"}}, "category": "Sm"}, {"key": "2226", "mappings": {"default": {"default": "non parallelo a"}}, "category": "Sm"}, {"key": "2227", "mappings": {"default": {"default": "e"}}, "category": "Sm"}, {"key": "2228", "mappings": {"default": {"default": "o"}}, "category": "Sm"}, {"key": "2229", "mappings": {"default": {"default": "intersezione"}}, "category": "Sm"}, {"key": "222A", "mappings": {"default": {"default": "unione"}}, "category": "Sm"}, {"key": "222B", "mappings": {"default": {"default": "integrale"}}, "category": "Sm"}, {"key": "222C", "mappings": {"default": {"default": "integrale doppio"}}, "category": "Sm"}, {"key": "222D", "mappings": {"default": {"default": "integrale triplo"}}, "category": "Sm"}, {"key": "222E", "mappings": {"default": {"default": "integrale di contorno"}}, "category": "Sm"}, {"key": "222F", "mappings": {"default": {"default": "integrale di superficie"}}, "category": "Sm"}, {"key": "2230", "mappings": {"default": {"default": "integrale di volume"}}, "category": "Sm"}, {"key": "2231", "mappings": {"default": {"default": "integrale in senso orario"}}, "category": "Sm"}, {"key": "2232", "mappings": {"default": {"default": "integrale di contorno in senso orario"}}, "category": "Sm"}, {"key": "2233", "mappings": {"default": {"default": "integrale di contorno in senso anti-orario"}}, "category": "Sm"}, {"key": "2234", "mappings": {"default": {"default": "quindi"}}, "category": "Sm"}, {"key": "2235", "mappings": {"default": {"default": "poiché"}}, "category": "Sm"}, {"key": "2236", "mappings": {"default": {"default": "rapporto"}}, "category": "Sm"}, {"key": "2237", "mappings": {"default": {"default": "proporzione"}}, "category": "Sm"}, {"key": "2238", "mappings": {"default": {"default": "punto meno"}}, "category": "Sm"}, {"key": "2239", "mappings": {"default": {"default": "eccede"}}, "category": "Sm"}, {"key": "223A", "mappings": {"default": {"default": "proporzione geometrica"}}, "category": "Sm"}, {"key": "223B", "mappings": {"default": {"default": "omotetico"}}, "category": "Sm"}, {"key": "223C", "mappings": {"default": {"default": "tilde"}}, "category": "Sm"}, {"category": "Sm", "key": "223D", "mappings": {"default": {"default": "tilde invertita"}}}, {"category": "Sm", "key": "223E", "mappings": {"default": {"default": "s invertita"}}}, {"key": "223F", "mappings": {"default": {"default": "onda seno"}}, "category": "Sm"}, {"category": "Sm", "key": "2240", "mappings": {"default": {"default": "prodotto corona"}}}, {"key": "2241", "mappings": {"default": {"default": "non tilde"}}, "category": "Sm"}, {"key": "2242", "mappings": {"default": {"default": "meno tilde"}}, "category": "Sm"}, {"key": "2243", "mappings": {"default": {"default": "asintoticamente uguale a"}}, "category": "Sm"}, {"key": "2244", "mappings": {"default": {"default": "non asintoticamente uguale a"}}, "category": "Sm"}, {"key": "2245", "mappings": {"default": {"default": "approssimativamente uguale a"}}, "category": "Sm"}, {"key": "2246", "mappings": {"default": {"default": "approssimativamente ma non effettivamente uguale a"}}, "category": "Sm"}, {"key": "2247", "mappings": {"default": {"default": "né approssimativamente né effettivamente uguale a"}}, "category": "Sm"}, {"key": "2248", "mappings": {"default": {"default": "circa uguale a"}}, "category": "Sm"}, {"key": "2249", "mappings": {"default": {"default": "non circa uguale a"}}, "category": "Sm"}, {"key": "224A", "mappings": {"default": {"default": "uguale circa o uguale a"}}, "category": "Sm"}, {"key": "224B", "mappings": {"default": {"default": "tripla tilde"}}, "category": "Sm"}, {"key": "224C", "mappings": {"default": {"default": "tutto uguale a"}}, "category": "Sm"}, {"key": "224D", "mappings": {"default": {"default": "equivalente a"}}, "category": "Sm"}, {"key": "224E", "mappings": {"default": {"default": "geometricamente equivalente a"}}, "category": "Sm"}, {"key": "224F", "mappings": {"default": {"default": "differenza fra"}}, "category": "Sm"}, {"key": "2250", "mappings": {"default": {"default": "si avvicina al limite"}}, "category": "Sm"}, {"key": "2251", "mappings": {"default": {"default": "geometricamente uguale a"}}, "category": "Sm"}, {"key": "2252", "mappings": {"default": {"default": "approssimatamente uguale a o immagine di"}}, "category": "Sm"}, {"key": "2253", "mappings": {"default": {"default": "immagine di o approssimatamente uguale a"}}, "category": "Sm"}, {"category": "Sm", "key": "2254", "mappings": {"default": {"default": "due punti uguale"}}}, {"category": "Sm", "key": "2255", "mappings": {"default": {"default": "uguale due punti"}}}, {"category": "Sm", "key": "2256", "mappings": {"default": {"default": "anello in uguale a"}}}, {"category": "Sm", "key": "2257", "mappings": {"default": {"default": "anello uguale a"}}}, {"key": "2258", "mappings": {"default": {"default": "corrisponde a"}}, "category": "Sm"}, {"key": "2259", "mappings": {"default": {"default": "stima"}}, "category": "Sm"}, {"key": "225A", "mappings": {"default": {"default": "equiangolare a"}}, "category": "Sm"}, {"key": "225B", "mappings": {"default": {"default": "star uguale a"}}, "category": "Sm"}, {"key": "225C", "mappings": {"default": {"default": "delta uguale a"}}, "category": "Sm"}, {"key": "225D", "mappings": {"default": {"default": "per definizione uguale a"}}, "category": "Sm"}, {"key": "225E", "mappings": {"default": {"default": "mi<PERSON><PERSON> da"}}, "category": "Sm"}, {"key": "225F", "mappings": {"default": {"default": "uguale con punto di domanda"}}, "category": "Sm"}, {"key": "2260", "mappings": {"default": {"default": "diverso da"}}, "category": "Sm"}, {"key": "2261", "mappings": {"default": {"default": "identico a"}}, "category": "Sm"}, {"key": "2262", "mappings": {"default": {"default": "non identico a"}}, "category": "Sm"}, {"key": "2263", "mappings": {"default": {"default": "strettamente equivalente a"}}, "category": "Sm"}, {"key": "2264", "mappings": {"default": {"default": "minore o uguale a"}}, "category": "Sm"}, {"key": "2265", "mappings": {"default": {"default": "maggiore o uguale a"}}, "category": "Sm"}, {"key": "2266", "mappings": {"default": {"default": "minore di sopra uguale a"}}, "category": "Sm"}, {"key": "2267", "mappings": {"default": {"default": "maggiore di sopra uguale a"}}, "category": "Sm"}, {"key": "2268", "mappings": {"default": {"default": "minore di ma non uguale a"}}, "category": "Sm"}, {"key": "2269", "mappings": {"default": {"default": "maggiore di ma non uguale a"}}, "category": "Sm"}, {"key": "226A", "mappings": {"default": {"default": "molto minore di"}}, "category": "Sm"}, {"key": "226B", "mappings": {"default": {"default": "molto maggiore di"}}, "category": "Sm"}, {"key": "226C", "mappings": {"default": {"default": "fra"}}, "category": "Sm"}, {"key": "226D", "mappings": {"default": {"default": "non e' equivalente a"}}, "category": "Sm"}, {"key": "226E", "mappings": {"default": {"default": "non minore di"}}, "category": "Sm"}, {"key": "226F", "mappings": {"default": {"default": "non maggiore di"}}, "category": "Sm"}, {"key": "2270", "mappings": {"default": {"default": "né minore di né uguale a"}}, "category": "Sm"}, {"key": "2271", "mappings": {"default": {"default": "né maggiore di o uguale a"}}, "category": "Sm"}, {"key": "2272", "mappings": {"default": {"default": "minore o uguale a"}}, "category": "Sm"}, {"key": "2273", "mappings": {"default": {"default": "maggiore o uguale a"}}, "category": "Sm"}, {"key": "2274", "mappings": {"default": {"default": "né minore di né equivalente a"}}, "category": "Sm"}, {"key": "2275", "mappings": {"default": {"default": "né maggiore di né equivalente a"}}, "category": "Sm"}, {"key": "2276", "mappings": {"default": {"default": "minore o maggiore di"}}, "category": "Sm"}, {"key": "2277", "mappings": {"default": {"default": "maggiore o minore di"}}, "category": "Sm"}, {"key": "2278", "mappings": {"default": {"default": "né minore di né maggiore di"}}, "category": "Sm"}, {"key": "2279", "mappings": {"default": {"default": "né maggiore di né minore di"}}, "category": "Sm"}, {"key": "227A", "mappings": {"default": {"default": "precede"}}, "category": "Sm"}, {"key": "227B", "mappings": {"default": {"default": "segue"}}, "category": "Sm"}, {"key": "227C", "mappings": {"default": {"default": "precede o equivale a"}}, "category": "Sm"}, {"key": "227D", "mappings": {"default": {"default": "segue o equivale a"}}, "category": "Sm"}, {"key": "227E", "mappings": {"default": {"default": "precede o equivale a"}}, "category": "Sm"}, {"key": "227F", "mappings": {"default": {"default": "segue o equivale a"}}, "category": "Sm"}, {"key": "2280", "mappings": {"default": {"default": "non precede"}}, "category": "Sm"}, {"key": "2281", "mappings": {"default": {"default": "non segue"}}, "category": "Sm"}, {"key": "2282", "mappings": {"default": {"default": "sottoinsieme di"}}, "category": "Sm"}, {"key": "2283", "mappings": {"default": {"default": "superinsieme di"}}, "category": "Sm"}, {"key": "2284", "mappings": {"default": {"default": "non un sottoinsieme di"}}, "category": "Sm"}, {"key": "2285", "mappings": {"default": {"default": "non un super insieme di"}}, "category": "Sm"}, {"key": "2286", "mappings": {"default": {"default": "sottoinsieme di o uguale a"}}, "category": "Sm"}, {"key": "2287", "mappings": {"default": {"default": "superinsieme di o uguale a"}}, "category": "Sm"}, {"key": "2288", "mappings": {"default": {"default": "né un sottoinsieme di né uguale a"}}, "category": "Sm"}, {"key": "2289", "mappings": {"default": {"default": "né un super insieme di né uguale a"}}, "category": "Sm"}, {"key": "228A", "mappings": {"default": {"default": "sottoinsieme di o non uguale a"}}, "category": "Sm"}, {"key": "228B", "mappings": {"default": {"default": "super insieme di o non uguale a"}}, "category": "Sm"}, {"key": "228C", "mappings": {"default": {"default": "multi insieme"}}, "category": "Sm"}, {"key": "228D", "mappings": {"default": {"default": "moltiplicazione multi insieme"}}, "category": "Sm"}, {"key": "228E", "mappings": {"default": {"default": "unione multi insieme"}}, "category": "Sm"}, {"category": "Sm", "key": "228F", "mappings": {"default": {"default": "immagine quadrata di"}}}, {"category": "Sm", "key": "2290", "mappings": {"default": {"default": "quadrato originale di"}}}, {"category": "Sm", "key": "2291", "mappings": {"default": {"default": "immagine quadrata di o uguale a"}}}, {"category": "Sm", "key": "2292", "mappings": {"default": {"default": "quadrato originale di o uguale a"}}}, {"category": "Sm", "key": "2293", "mappings": {"default": {"default": "cappuccio quadrato"}}}, {"category": "Sm", "key": "2294", "mappings": {"default": {"default": "coppa quadrata"}}}, {"category": "Sm", "key": "2295", "mappings": {"default": {"default": "più cer<PERSON>to"}}}, {"category": "Sm", "key": "2296", "mappings": {"default": {"default": "meno cerchiato"}}}, {"category": "Sm", "key": "2297", "mappings": {"default": {"default": "moltiplicazione cerchiata"}}}, {"category": "Sm", "key": "2298", "mappings": {"default": {"default": "divisione cerchiata"}}}, {"key": "2299", "mappings": {"default": {"default": "punto cerchiato"}}, "category": "Sm"}, {"category": "Sm", "key": "229A", "mappings": {"default": {"default": "operatore ad anello cerchiato"}}}, {"category": "Sm", "key": "229B", "mappings": {"default": {"default": "operatore di asterisco cerchiato"}}}, {"category": "Sm", "key": "229C", "mappings": {"default": {"default": "uguale cerchiato"}}}, {"category": "Sm", "key": "229D", "mappings": {"default": {"default": "lineetta cerchiata"}}}, {"category": "Sm", "key": "229E", "mappings": {"default": {"default": "più squadrato"}}}, {"category": "Sm", "key": "229F", "mappings": {"default": {"default": "meno squadrato"}}}, {"category": "Sm", "key": "22A0", "mappings": {"default": {"default": "per squadrato"}}}, {"category": "Sm", "key": "22A1", "mappings": {"default": {"default": "operatore punto squadrato"}}}, {"category": "Sm", "key": "22A2", "mappings": {"default": {"default": "<PERSON>ello"}}}, {"category": "Sm", "key": "22A3", "mappings": {"default": {"default": "tornello inverso"}}}, {"category": "Sm", "key": "22A4", "mappings": {"default": {"default": "tornello verso il basso"}}}, {"key": "22A5", "mappings": {"default": {"default": "perpendicolare"}}, "category": "Sm"}, {"key": "22A6", "mappings": {"default": {"default": "asserzione"}}, "category": "Sm"}, {"key": "22A7", "mappings": {"default": {"default": "modella"}}, "category": "Sm"}, {"key": "22A8", "mappings": {"default": {"default": "vero"}}, "category": "Sm"}, {"key": "22A9", "mappings": {"default": {"default": "forza"}}, "category": "Sm"}, {"category": "Sm", "key": "22AA", "mappings": {"default": {"default": "tornello verticale a tripla barra"}}}, {"category": "Sm", "key": "22AB", "mappings": {"default": {"default": "tornello verticale a doppia barra"}}}, {"key": "22AC", "mappings": {"default": {"default": "non prova"}}, "category": "Sm"}, {"key": "22AD", "mappings": {"default": {"default": "non vero"}}, "category": "Sm"}, {"key": "22AE", "mappings": {"default": {"default": "non forza"}}, "category": "Sm"}, {"category": "Sm", "key": "22AF", "mappings": {"default": {"default": "tornello verticale a doppia barra negata"}}}, {"key": "22B0", "mappings": {"default": {"default": "precede sotto la relazione di"}}, "category": "Sm"}, {"key": "22B1", "mappings": {"default": {"default": "segue sotto la relazione di"}}, "category": "Sm"}, {"key": "22B2", "mappings": {"default": {"default": "sottogruppo normale di"}}, "category": "Sm"}, {"key": "22B3", "mappings": {"default": {"default": "contiene come sottogruppo normale"}}, "category": "Sm"}, {"key": "22B4", "mappings": {"default": {"default": "sottogruppo normale di o uguale a"}}, "category": "Sm"}, {"key": "22B5", "mappings": {"default": {"default": "contiene come sottogruppo normale o uguale a"}}, "category": "Sm"}, {"key": "22B6", "mappings": {"default": {"default": "originale di"}}, "category": "Sm"}, {"key": "22B7", "mappings": {"default": {"default": "immagine di"}}, "category": "Sm"}, {"category": "Sm", "key": "22B8", "mappings": {"default": {"default": "multimappa"}}}, {"category": "Sm", "key": "22B9", "mappings": {"default": {"default": "matrice coniugale ermitica"}}}, {"category": "Sm", "key": "22BA", "mappings": {"default": {"default": "intercalare"}}}, {"category": "Sm", "key": "22BB", "mappings": {"default": {"default": "xor"}}}, {"category": "Sm", "key": "22BC", "mappings": {"default": {"default": "nand"}}}, {"category": "Sm", "key": "22BD", "mappings": {"default": {"default": "nor"}}}, {"category": "Sm", "key": "22BF", "mappings": {"default": {"default": "triangolo rettangolo"}}}, {"category": "Sm", "key": "22C0", "mappings": {"default": {"default": "and"}}}, {"category": "Sm", "key": "22C1", "mappings": {"default": {"default": "or"}}}, {"key": "22C2", "mappings": {"default": {"default": "intersezione"}}, "category": "Sm"}, {"key": "22C3", "mappings": {"default": {"default": "unione"}}, "category": "Sm"}, {"category": "Sm", "key": "22C4", "mappings": {"default": {"default": "operatore diamante"}}}, {"key": "22C5", "mappings": {"default": {"default": "punto"}}, "category": "Sm"}, {"category": "Sm", "key": "22C6", "mappings": {"default": {"default": "operatore star"}}}, {"category": "Sm", "key": "22C7", "mappings": {"default": {"default": "divisione con segno moltiplicazione"}}}, {"category": "Sm", "key": "22C8", "mappings": {"default": {"default": "cravatta a farfalla"}}}, {"category": "Sm", "key": "22C9", "mappings": {"default": {"default": "prodotto semidiretto fattore sinistro normale"}}}, {"category": "Sm", "key": "22CA", "mappings": {"default": {"default": "prodotto semidiretto fattore destro normale"}}}, {"category": "Sm", "key": "22CB", "mappings": {"default": {"default": "prodotto semidiretto sinistro"}}}, {"category": "Sm", "key": "22CC", "mappings": {"default": {"default": "prodotto semidiretto destro"}}}, {"category": "Sm", "key": "22CD", "mappings": {"default": {"default": "tilde invertita uguale"}}}, {"category": "Sm", "key": "22CE", "mappings": {"default": {"default": "or logico riccio"}}}, {"category": "Sm", "key": "22CF", "mappings": {"default": {"default": "e logico riccio"}}}, {"key": "22D0", "mappings": {"default": {"default": "do<PERSON>io sotto<PERSON>me"}}, "category": "Sm"}, {"key": "22D1", "mappings": {"default": {"default": "doppio superinsieme"}}, "category": "Sm"}, {"key": "22D2", "mappings": {"default": {"default": "doppia intersezione"}}, "category": "Sm"}, {"key": "22D3", "mappings": {"default": {"default": "doppia unione"}}, "category": "Sm"}, {"category": "Sm", "key": "22D4", "mappings": {"default": {"default": "intersezione corretta"}}}, {"key": "22D5", "mappings": {"default": {"default": "uguale e parallelo a"}}, "category": "Sm"}, {"key": "22D6", "mappings": {"default": {"default": "minore di con punto"}}, "category": "Sm"}, {"key": "22D7", "mappings": {"default": {"default": "maggiore di con punto"}}, "category": "Sm"}, {"key": "22D8", "mappings": {"default": {"default": "molto minore di"}}, "category": "Sm"}, {"key": "22D9", "mappings": {"default": {"default": "molto maggiore di"}}, "category": "Sm"}, {"key": "22DA", "mappings": {"default": {"default": "minore di o uguale a o maggiore di"}}, "category": "Sm"}, {"key": "22DB", "mappings": {"default": {"default": "maggiore di o uguale a o minore di"}}, "category": "Sm"}, {"key": "22DC", "mappings": {"default": {"default": "uguale o minore di"}}, "category": "Sm"}, {"key": "22DD", "mappings": {"default": {"default": "uguale o maggiore di"}}, "category": "Sm"}, {"key": "22DE", "mappings": {"default": {"default": "uguale a o precede"}}, "category": "Sm"}, {"key": "22DF", "mappings": {"default": {"default": "uguale a o segue"}}, "category": "Sm"}, {"key": "22E0", "mappings": {"default": {"default": "non precede né eguaglia"}}, "category": "Sm"}, {"key": "22E1", "mappings": {"default": {"default": "non segue né eguaglia"}}, "category": "Sm"}, {"category": "Sm", "key": "22E2", "mappings": {"default": {"default": "non è immagine quadrata di o uguale a"}}}, {"category": "Sm", "key": "22E3", "mappings": {"default": {"default": "non è originale quadrato di o uguale a"}}}, {"category": "Sm", "key": "22E4", "mappings": {"default": {"default": "immagine quadrata di o diverso da"}}}, {"category": "Sm", "key": "22E5", "mappings": {"default": {"default": "originale quadrato uguale o diverso da"}}}, {"key": "22E6", "mappings": {"default": {"default": "minore di ma non equivalente a"}}, "category": "Sm"}, {"key": "22E7", "mappings": {"default": {"default": "maggiore di ma non equivalente a"}}, "category": "Sm"}, {"key": "22E8", "mappings": {"default": {"default": "precede ma non equivale a"}}, "category": "Sm"}, {"key": "22E9", "mappings": {"default": {"default": "segue ma non equivale a"}}, "category": "Sm"}, {"key": "22EA", "mappings": {"default": {"default": "non sottogruppo normale"}}, "category": "Sm"}, {"key": "22EB", "mappings": {"default": {"default": "non contiene come sottogruppo normale"}}, "category": "Sm"}, {"key": "22EC", "mappings": {"default": {"default": "non sottogruppo normale o uguale a"}}, "category": "Sm"}, {"key": "22ED", "mappings": {"default": {"default": "non contiene come sottogruppo normale o uguale"}}, "category": "Sm"}, {"key": "22EE", "mappings": {"default": {"default": "puntini in verticale"}}, "category": "Sm"}, {"key": "22EF", "mappings": {"default": {"default": "puntini in orizzontale"}}, "category": "Sm"}, {"key": "22F0", "mappings": {"default": {"default": "puntini in diagonale verso alto destra"}}, "category": "Sm"}, {"key": "22F1", "mappings": {"default": {"default": "puntini in diagonale verso basso destra"}}, "category": "Sm"}, {"category": "Sm", "key": "22F2", "mappings": {"default": {"default": "elemento di con tratto orizzontale lungo"}}}, {"category": "Sm", "key": "22F3", "mappings": {"default": {"default": "elemento con barra verticale all'estremità del tratto orizzontale"}}}, {"category": "Sm", "key": "22F4", "mappings": {"default": {"default": "piccolo elemento di con barra verticale all'estremità del tratto orizzontale"}}}, {"category": "Sm", "key": "22F5", "mappings": {"default": {"default": "elemento di con punto sopra"}}}, {"category": "Sm", "key": "22F6", "mappings": {"default": {"default": "elemento di con barra sopra"}}}, {"category": "Sm", "key": "22F7", "mappings": {"default": {"default": "piccolo elemento di con barra sopra"}}}, {"category": "Sm", "key": "22F8", "mappings": {"default": {"default": "elemento di con barra sotto"}}}, {"category": "Sm", "key": "22F9", "mappings": {"default": {"default": "elemento di con due tratti oriz<PERSON>i"}}}, {"category": "Sm", "key": "22FA", "mappings": {"default": {"default": "contiene un tratto orizzontale lungo"}}}, {"category": "Sm", "key": "22FB", "mappings": {"default": {"default": "contiene con barra verticale alla fine del trato orizzontale"}}}, {"category": "Sm", "key": "22FC", "mappings": {"default": {"default": "piccoli contenitori con barra verticale all'estremità del tratto orizzontale"}}}, {"category": "Sm", "key": "22FD", "mappings": {"default": {"default": "contiene con barra sopra"}}}, {"category": "Sm", "key": "22FE", "mappings": {"default": {"default": "contiene piccolo con barra sopra"}}}, {"category": "Sm", "key": "22FF", "mappings": {"default": {"default": "appartiene a con notazione z"}}}, {"key": "2300", "mappings": {"default": {"default": "diametro"}}, "category": "So"}, {"category": "So", "key": "2302", "mappings": {"default": {"default": "casa"}}}, {"category": "So", "key": "2305", "mappings": {"default": {"default": "proiettiva"}}}, {"category": "So", "key": "2306", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"category": "So", "key": "2307", "mappings": {"default": {"default": "linea ondulata"}}}, {"category": "So", "key": "2310", "mappings": {"default": {"default": "segno not"}}}, {"category": "So", "key": "2311", "mappings": {"default": {"default": "losanga quadrata"}}}, {"key": "2312", "mappings": {"default": {"default": "arco"}}, "category": "So"}, {"key": "2313", "mappings": {"default": {"default": "segmento"}}, "category": "So"}, {"category": "So", "key": "2314", "mappings": {"default": {"default": "settore"}}}, {"category": "So", "key": "2795", "mappings": {"default": {"default": "segno più grasetto"}}}, {"category": "So", "key": "2796", "mappings": {"default": {"default": "segno meno grassetto"}}}, {"category": "So", "key": "2797", "mappings": {"default": {"default": "segno di divisione grassetto"}}}, {"category": "So", "key": "27B0", "mappings": {"default": {"default": "ciclo riccio"}}}, {"category": "So", "key": "27BF", "mappings": {"default": {"default": "ciclo riccio do<PERSON>io"}}}, {"category": "Sm", "key": "27C1", "mappings": {"default": {"default": "triangolo bianco contenente un piccolo triangolo bianco"}}}, {"category": "Sm", "key": "27C2", "mappings": {"default": {"default": "perpendicolare "}}}, {"category": "Sm", "key": "27C3", "mappings": {"default": {"default": "sottoinsieme aperto"}}}, {"category": "Sm", "key": "27C4", "mappings": {"default": {"default": "superinseime aperto"}}}, {"category": "Sm", "key": "27C7", "mappings": {"default": {"default": "o con punto"}}}, {"category": "Sm", "key": "27C8", "mappings": {"default": {"default": "barra diagonale prima di sottoinsieme"}}}, {"category": "Sm", "key": "27C9", "mappings": {"default": {"default": "superinsieme prima di barra diagonale"}}}, {"category": "Sm", "key": "27CA", "mappings": {"default": {"default": "barra verticale con tratto orizzontale"}}}, {"category": "Sm", "key": "27CB", "mappings": {"default": {"default": "diagonale ascendente"}}}, {"category": "Sm", "key": "27CC", "mappings": {"default": {"default": "divisione lunga"}}}, {"category": "Sm", "key": "27CD", "mappings": {"default": {"default": "diagonale cadente"}}}, {"category": "Sm", "key": "27CE", "mappings": {"default": {"default": "and squadrato"}}}, {"category": "Sm", "key": "27CF", "mappings": {"default": {"default": "or squadrato"}}}, {"category": "Sm", "key": "27D0", "mappings": {"default": {"default": "diamante bianco con punto centrale"}}}, {"category": "Sm", "key": "27D1", "mappings": {"default": {"default": "and con punto"}}}, {"category": "Sm", "key": "27D2", "mappings": {"default": {"default": "elemento di apertura verso l'alto"}}}, {"category": "Sm", "key": "27D3", "mappings": {"default": {"default": "angolo in basso a destra con punto"}}}, {"category": "Sm", "key": "27D4", "mappings": {"default": {"default": "angolo superiore sinistro con punto"}}}, {"category": "Sm", "key": "27D5", "mappings": {"default": {"default": "join esterno sinistro"}}}, {"category": "Sm", "key": "27D6", "mappings": {"default": {"default": "join esterno destro"}}}, {"category": "Sm", "key": "27D7", "mappings": {"default": {"default": "join esterno pieno"}}}, {"category": "Sm", "key": "27D8", "mappings": {"default": {"default": "tornello grande"}}}, {"category": "Sm", "key": "27D9", "mappings": {"default": {"default": "tornello grande verso il basso"}}}, {"category": "Sm", "key": "27DA", "mappings": {"default": {"default": "doppio tornello a destra e a sinistra"}}}, {"category": "Sm", "key": "27DB", "mappings": {"default": {"default": "tornello sinistro e destro"}}}, {"category": "Sm", "key": "27DC", "mappings": {"default": {"default": "multimappa sinistra"}}}, {"category": "Sm", "key": "27DD", "mappings": {"default": {"default": "tornello lungo destro"}}}, {"category": "Sm", "key": "27DE", "mappings": {"default": {"default": "tornello lungo sinistro"}}}, {"category": "Sm", "key": "27DF", "mappings": {"default": {"default": "tornello in altro con cerchio sopra"}}}, {"category": "Sm", "key": "27E0", "mappings": {"default": {"default": "losanga divisa da linea orizzonatel"}}}, {"category": "Sm", "key": "27E1", "mappings": {"default": {"default": "diamante concavo bianco"}}}, {"category": "Sm", "key": "27E2", "mappings": {"default": {"default": "diamante bianco a punta concava con segno di spunta a sinistra"}}}, {"category": "Sm", "key": "27E3", "mappings": {"default": {"default": "diamante bianco a punta concava con segno di spunta a destra"}}}, {"category": "Sm", "key": "27E4", "mappings": {"default": {"default": "quadrato bianco con segno di spunta a sinistra"}}}, {"category": "Sm", "key": "27E5", "mappings": {"default": {"default": "quadrato bianco con segno di spunta a destra"}}}, {"category": "Sm", "key": "292B", "mappings": {"default": {"default": "diagonale crescente incrociante diagonale cadente"}}}, {"category": "Sm", "key": "292C", "mappings": {"default": {"default": "diagonale cadente incrociante diagonale crescente"}}}, {"category": "Sm", "key": "2980", "mappings": {"default": {"default": "delimitatore a tripla barra verticale"}}}, {"category": "Sm", "key": "2981", "mappings": {"default": {"default": "punto a notatione Z "}}}, {"category": "Sm", "key": "2982", "mappings": {"default": {"default": "due punti a notazione Z"}}}, {"category": "Sm", "key": "2999", "mappings": {"default": {"default": "delimitatore punteggiato"}}}, {"category": "Sm", "key": "299A", "mappings": {"default": {"default": "linea verticale a zig-zag"}}}, {"category": "Sm", "key": "29B0", "mappings": {"default": {"default": "set vuoto invertito"}}}, {"category": "Sm", "key": "29B1", "mappings": {"default": {"default": "set vuoto con barra"}}}, {"category": "Sm", "key": "29B2", "mappings": {"default": {"default": "set vuoto con piccolo cerchio sopra"}}}, {"category": "Sm", "key": "29B5", "mappings": {"default": {"default": "cerchio con barra orizzontale"}}}, {"category": "Sm", "key": "29B6", "mappings": {"default": {"default": "barra verticale cerchiata"}}}, {"category": "Sm", "key": "29B7", "mappings": {"default": {"default": "parallelo cerchiato"}}}, {"category": "Sm", "key": "29B8", "mappings": {"default": {"default": "solido inverso cerchiato"}}}, {"category": "Sm", "key": "29B9", "mappings": {"default": {"default": "perpendicolare cerchiato"}}}, {"category": "Sm", "key": "29BA", "mappings": {"default": {"default": "cerchio diviso da barra orizzontale e metà superiore diviso da barra verticale"}}}, {"category": "Sm", "key": "29BB", "mappings": {"default": {"default": "cerchio con X sovrapposta"}}}, {"category": "Sm", "key": "29BC", "mappings": {"default": {"default": "segno di divisione ruotato in senso antiorario"}}}, {"category": "Sm", "key": "29BE", "mappings": {"default": {"default": "punto elenco vuoto cerchiato"}}}, {"category": "Sm", "key": "29BF", "mappings": {"default": {"default": "punto elenco cer<PERSON>to"}}}, {"category": "Sm", "key": "29C0", "mappings": {"default": {"default": "minore di cerchiato"}}}, {"category": "Sm", "key": "29C1", "mappings": {"default": {"default": "maggiore di cerchiato"}}}, {"category": "Sm", "key": "29C2", "mappings": {"default": {"default": "cerchio con piccolo cerchio a destra"}}}, {"category": "Sm", "key": "29C3", "mappings": {"default": {"default": "cerchio con due tratti or<PERSON>i a destra"}}}, {"category": "Sm", "key": "29C4", "mappings": {"default": {"default": "barra diagonale crescente squadrata"}}}, {"category": "Sm", "key": "29C5", "mappings": {"default": {"default": "barra diagonale discendente squadrata"}}}, {"category": "Sm", "key": "29C6", "mappings": {"default": {"default": "asterisco squadrato"}}}, {"category": "Sm", "key": "29C7", "mappings": {"default": {"default": "piccolo cerchio squadrato"}}}, {"category": "Sm", "key": "29C8", "mappings": {"default": {"default": "quadrato squadrato"}}}, {"category": "Sm", "key": "29C9", "mappings": {"default": {"default": "due quadrati uniti"}}}, {"category": "Sm", "key": "29CA", "mappings": {"default": {"default": "triangolo con punto sopra"}}}, {"category": "Sm", "key": "29CB", "mappings": {"default": {"default": "triangolo con barra sotto"}}}, {"category": "Sm", "key": "29CC", "mappings": {"default": {"default": "s in triangolo"}}}, {"category": "Sm", "key": "29CD", "mappings": {"default": {"default": "triangolo con Ser<PERSON><PERSON> in basso"}}}, {"category": "Sm", "key": "29CE", "mappings": {"default": {"default": "triangolo destro sopra triangolo sinistro"}}}, {"category": "Sm", "key": "29CF", "mappings": {"default": {"default": "triangolo sinistro accanto a barra verticale"}}}, {"category": "Sm", "key": "29D0", "mappings": {"default": {"default": "barra verticale accanto a triangolo destro"}}}, {"category": "Sm", "key": "29D1", "mappings": {"default": {"default": "farfalla con metà sinistra nera"}}}, {"category": "Sm", "key": "29D2", "mappings": {"default": {"default": "farfalla con il giusto mezzo nero"}}}, {"category": "Sm", "key": "29D3", "mappings": {"default": {"default": "farfalla nera"}}}, {"category": "Sm", "key": "29D4", "mappings": {"default": {"default": "moltiplicazione con metà sinistra nera"}}}, {"category": "Sm", "key": "29D5", "mappings": {"default": {"default": "moltiplicazione con metà destra nera"}}}, {"category": "Sm", "key": "29D6", "mappings": {"default": {"default": "cless<PERSON><PERSON> bianca"}}}, {"category": "Sm", "key": "29D7", "mappings": {"default": {"default": "cless<PERSON>ra nera"}}}, {"category": "Sm", "key": "29DC", "mappings": {"default": {"default": "infinito incompleto"}}}, {"category": "Sm", "key": "29DD", "mappings": {"default": {"default": "legatura sopra infinito"}}}, {"category": "Sm", "key": "29DE", "mappings": {"default": {"default": "infinito negato con barra verticale"}}}, {"category": "Sm", "key": "29DF", "mappings": {"default": {"default": "multimappa a doppia estremità"}}}, {"category": "Sm", "key": "29E0", "mappings": {"default": {"default": "quadrato con contorno sagomato"}}}, {"category": "Sm", "key": "29E1", "mappings": {"default": {"default": "aumenta come"}}}, {"category": "Sm", "key": "29E2", "mappings": {"default": {"default": "pro<PERSON><PERSON>"}}}, {"category": "Sm", "key": "29E3", "mappings": {"default": {"default": "segno uguale e parallelo inclinato"}}}, {"category": "Sm", "key": "29E4", "mappings": {"default": {"default": "segno uguale e inclinato parallelo con tilde sopra"}}}, {"category": "Sm", "key": "29E5", "mappings": {"default": {"default": "parallelo identico a e inclinato"}}}, {"category": "Sm", "key": "29E6", "mappings": {"default": {"default": "equivalente tautologico"}}}, {"category": "Sm", "key": "29E7", "mappings": {"default": {"default": "termodinamico"}}}, {"category": "Sm", "key": "29E8", "mappings": {"default": {"default": "triangolo verso il basso con metà sinistra nera"}}}, {"category": "Sm", "key": "29E9", "mappings": {"default": {"default": "triangolo verso il basso con metà destra nera"}}}, {"category": "Sm", "key": "29EB", "mappings": {"default": {"default": "losanga nera"}}}, {"category": "Sm", "key": "29EE", "mappings": {"default": {"default": "quadrato bianco con barre errori"}}}, {"category": "Sm", "key": "29EF", "mappings": {"default": {"default": "quadrato nero con barre errori"}}}, {"category": "Sm", "key": "29F0", "mappings": {"default": {"default": "diamante bianco con barre errore"}}}, {"category": "Sm", "key": "29F1", "mappings": {"default": {"default": "diamante nero con barre errori"}}}, {"category": "Sm", "key": "29F2", "mappings": {"default": {"default": "cerchio bianco con barre errori"}}}, {"category": "Sm", "key": "29F3", "mappings": {"default": {"default": "cerchio nero con barre errori"}}}, {"category": "Sm", "key": "29F4", "mappings": {"default": {"default": "regola ritardata"}}}, {"category": "Sm", "key": "29F5", "mappings": {"default": {"default": "slash inverso"}}}, {"category": "Sm", "key": "29F6", "mappings": {"default": {"default": "slash con barra sopra"}}}, {"category": "Sm", "key": "29F7", "mappings": {"default": {"default": "solido inverso con tratto orizzontale"}}}, {"category": "Sm", "key": "29F8", "mappings": {"default": {"default": "slash grande"}}}, {"category": "Sm", "key": "29F9", "mappings": {"default": {"default": "backslash grande"}}}, {"category": "Sm", "key": "29FA", "mappings": {"default": {"default": "più doppio"}}}, {"category": "Sm", "key": "29FB", "mappings": {"default": {"default": "pi<PERSON> triplo"}}}, {"category": "Sm", "key": "29FE", "mappings": {"default": {"default": "minuscolo"}}}, {"category": "Sm", "key": "29FF", "mappings": {"default": {"default": "mini"}}}, {"category": "Sm", "key": "2A00", "mappings": {"default": {"default": "punto cerchiato"}}}, {"category": "Sm", "key": "2A01", "mappings": {"default": {"default": "più cer<PERSON>to"}}}, {"category": "Sm", "key": "2A02", "mappings": {"default": {"default": "per cerchiato"}}}, {"category": "Sm", "key": "2A03", "mappings": {"default": {"default": "unione con punto"}}}, {"category": "Sm", "key": "2A04", "mappings": {"default": {"default": "unione con più"}}}, {"category": "Sm", "key": "2A05", "mappings": {"default": {"default": "intersezione quadrata"}}}, {"category": "Sm", "key": "2A06", "mappings": {"default": {"default": "unione quadrata"}}}, {"category": "Sm", "key": "2A07", "mappings": {"default": {"default": "due e logici"}}}, {"category": "Sm", "key": "2A08", "mappings": {"default": {"default": "due o logici"}}}, {"category": "Sm", "key": "2A09", "mappings": {"default": {"default": "per"}}}, {"category": "Sm", "key": "2A0A", "mappings": {"default": {"default": "sommatoria modulo due"}}}, {"category": "Sm", "key": "2A0B", "mappings": {"default": {"default": "sommatoria con integrale"}}}, {"category": "Sm", "key": "2A0C", "mappings": {"default": {"default": "operatore integrale quadruplo"}}}, {"category": "Sm", "key": "2A0D", "mappings": {"default": {"default": "integrale parte finita"}}}, {"category": "Sm", "key": "2A0E", "mappings": {"default": {"default": "integrale con doppio trattino"}}}, {"category": "Sm", "key": "2A0F", "mappings": {"default": {"default": "media integrale con barra"}}}, {"category": "Sm", "key": "2A10", "mappings": {"default": {"default": "funzione di circolazione"}}}, {"category": "Sm", "key": "2A11", "mappings": {"default": {"default": "integrazione antioraria"}}}, {"category": "Sm", "key": "2A12", "mappings": {"default": {"default": "integrazione di linea con percorso rettangolare attorno al polo"}}}, {"category": "Sm", "key": "2A13", "mappings": {"default": {"default": "integrazione di linea con percorso semicircolare attorno al polo"}}}, {"category": "Sm", "key": "2A14", "mappings": {"default": {"default": "integrazione della linea non compresa il polo"}}}, {"category": "Sm", "key": "2A15", "mappings": {"default": {"default": "integrale attorno a un punto"}}}, {"category": "Sm", "key": "2A16", "mappings": {"default": {"default": "integrale quaternion"}}}, {"category": "Sm", "key": "2A18", "mappings": {"default": {"default": "integrale con segno per"}}}, {"category": "Sm", "key": "2A19", "mappings": {"default": {"default": "integrale con intersezione"}}}, {"category": "Sm", "key": "2A1A", "mappings": {"default": {"default": "integrale con unione"}}}, {"category": "Sm", "key": "2A1B", "mappings": {"default": {"default": "integrale con barra sopra"}}}, {"category": "Sm", "key": "2A1C", "mappings": {"default": {"default": "integrale con barra sotto"}}}, {"category": "Sm", "key": "2A1D", "mappings": {"default": {"default": "unione"}}}, {"category": "Sm", "key": "2A1E", "mappings": {"default": {"default": "triangolo sinistro grande"}}}, {"category": "Sm", "key": "2A1F", "mappings": {"default": {"default": "composizione di schema a notazione Z"}}}, {"category": "Sm", "key": "2A20", "mappings": {"default": {"default": "piping di schema a notazione Z"}}}, {"category": "Sm", "key": "2A21", "mappings": {"default": {"default": "proiezione a schema di notazione Z"}}}, {"key": "2A22", "mappings": {"default": {"default": "più con cerchio sopra"}}, "category": "Sm"}, {"key": "2A23", "mappings": {"default": {"default": "più con cappuccio sopra"}}, "category": "Sm"}, {"key": "2A24", "mappings": {"default": {"default": "tilde con più sotto"}}, "category": "Sm"}, {"key": "2A25", "mappings": {"default": {"default": "più con punto sotto"}}, "category": "Sm"}, {"key": "2A26", "mappings": {"default": {"default": "tilde con più sopra"}}, "category": "Sm"}, {"key": "2A27", "mappings": {"default": {"default": "segno più con pedice due"}}, "category": "Sm"}, {"category": "Sm", "key": "2A28", "mappings": {"default": {"default": "segno più con triangolo nero"}}}, {"key": "2A29", "mappings": {"default": {"default": "segno meno con virgola sopra"}}, "category": "Sm"}, {"key": "2A2A", "mappings": {"default": {"default": "segno meno con punto sotto"}}, "category": "Sm"}, {"category": "Sm", "key": "2A2B", "mappings": {"default": {"default": "segno meno con punti cadenti"}}}, {"category": "Sm", "key": "2A2C", "mappings": {"default": {"default": "segno meno con punti crescenti"}}}, {"category": "Sm", "key": "2A2D", "mappings": {"default": {"default": "segno più nel semicerchio sinistro"}}}, {"category": "Sm", "key": "2A2E", "mappings": {"default": {"default": "segno più nel semicerchio destro"}}}, {"category": "Sm", "key": "2A2F", "mappings": {"default": {"default": "prodotto vettoriale o incrociato"}}}, {"category": "Sm", "key": "2A30", "mappings": {"default": {"default": "segno di moltiplicazione con punto sopra"}}}, {"category": "Sm", "key": "2A31", "mappings": {"default": {"default": "segno di moltiplicazione con sottobarra"}}}, {"category": "Sm", "key": "2A32", "mappings": {"default": {"default": "prodotto semidiretto con fondo chiuso"}}}, {"category": "Sm", "key": "2A33", "mappings": {"default": {"default": "prodotto smash "}}}, {"category": "Sm", "key": "2A34", "mappings": {"default": {"default": "segno di moltiplicazione in semicerchio sinistro"}}}, {"category": "Sm", "key": "2A35", "mappings": {"default": {"default": "segno di moltiplicazione in semicerchio destro"}}}, {"category": "Sm", "key": "2A36", "mappings": {"default": {"default": "segno di moltiplicazione cerchiato con accento circonflesso"}}}, {"category": "Sm", "key": "2A37", "mappings": {"default": {"default": "segno di moltiplicazione in doppio cerchio"}}}, {"category": "Sm", "key": "2A38", "mappings": {"default": {"default": "segno di divisione cerchiato"}}}, {"category": "Sm", "key": "2A39", "mappings": {"default": {"default": "più in triangolo"}}}, {"category": "Sm", "key": "2A3A", "mappings": {"default": {"default": "segno meno nel triangolo"}}}, {"category": "Sm", "key": "2A3B", "mappings": {"default": {"default": "triangolo di segno di moltiplicazione"}}}, {"category": "Sm", "key": "2A3C", "mappings": {"default": {"default": "prodotto interno"}}}, {"category": "Sm", "key": "2A3D", "mappings": {"default": {"default": "prodotto destro interno"}}}, {"category": "Sm", "key": "2A3E", "mappings": {"default": {"default": "composizione relazionale a notazione Z"}}}, {"category": "Sm", "key": "2A3F", "mappings": {"default": {"default": "amalgamazione o coprodotto"}}}, {"category": "Sm", "key": "2A40", "mappings": {"default": {"default": "intersezione con punto"}}}, {"category": "Sm", "key": "2A41", "mappings": {"default": {"default": "unione con segno meno"}}}, {"category": "Sm", "key": "2A42", "mappings": {"default": {"default": "unione con barra sopra"}}}, {"category": "Sm", "key": "2A43", "mappings": {"default": {"default": "intersezione con barra"}}}, {"category": "Sm", "key": "2A44", "mappings": {"default": {"default": "intersezione con and"}}}, {"category": "Sm", "key": "2A45", "mappings": {"default": {"default": "unione con or"}}}, {"category": "Sm", "key": "2A46", "mappings": {"default": {"default": "unione sopra intersezione"}}}, {"category": "Sm", "key": "2A47", "mappings": {"default": {"default": "intersezione sopra unione"}}}, {"category": "Sm", "key": "2A48", "mappings": {"default": {"default": "unione sopra barra sopra intersezione"}}}, {"category": "Sm", "key": "2A49", "mappings": {"default": {"default": "intersezione sopra la barra sopra l'Unione"}}}, {"category": "Sm", "key": "2A4A", "mappings": {"default": {"default": "unione accanto e unita con unione"}}}, {"category": "Sm", "key": "2A4B", "mappings": {"default": {"default": "intersezione accanto e congiunta con intersezione"}}}, {"category": "Sm", "key": "2A4C", "mappings": {"default": {"default": "unione chiusa con serif"}}}, {"category": "Sm", "key": "2A4D", "mappings": {"default": {"default": "intersezione chiusa con serif"}}}, {"category": "Sm", "key": "2A4E", "mappings": {"default": {"default": "doppia intersezione quadrata"}}}, {"category": "Sm", "key": "2A4F", "mappings": {"default": {"default": "doppia unione quadrata"}}}, {"category": "Sm", "key": "2A50", "mappings": {"default": {"default": "unione chiusa con serif e prodotto smash"}}}, {"category": "Sm", "key": "2A51", "mappings": {"default": {"default": "and con punto <PERSON>"}}}, {"category": "Sm", "key": "2A52", "mappings": {"default": {"default": "or con punto <PERSON>pra"}}}, {"category": "Sm", "key": "2A53", "mappings": {"default": {"default": "doppio and"}}}, {"category": "Sm", "key": "2A54", "mappings": {"default": {"default": "doppio or"}}}, {"category": "Sm", "key": "2A55", "mappings": {"default": {"default": "due and che si intersezionano"}}}, {"category": "Sm", "key": "2A56", "mappings": {"default": {"default": "due or che si intersezionano"}}}, {"category": "Sm", "key": "2A57", "mappings": {"default": {"default": "or pendente grande"}}}, {"category": "Sm", "key": "2A58", "mappings": {"default": {"default": "and pendente grande"}}}, {"category": "Sm", "key": "2A59", "mappings": {"default": {"default": "or che si sovrappone a and"}}}, {"category": "Sm", "key": "2A5A", "mappings": {"default": {"default": "and con gambo medio"}}}, {"category": "Sm", "key": "2A5B", "mappings": {"default": {"default": "or con gambo medio"}}}, {"category": "Sm", "key": "2A5C", "mappings": {"default": {"default": "and con trattino or<PERSON>"}}}, {"category": "Sm", "key": "2A5D", "mappings": {"default": {"default": "or con trattino oriz<PERSON>tale"}}}, {"category": "Sm", "key": "2A5E", "mappings": {"default": {"default": "and con doppia barra sopra"}}}, {"category": "Sm", "key": "2A5F", "mappings": {"default": {"default": "and con barra sotto"}}}, {"category": "Sm", "key": "2A60", "mappings": {"default": {"default": "and con doppia barra sotto"}}}, {"category": "Sm", "key": "2A61", "mappings": {"default": {"default": "v piccola con barra sotto"}}}, {"category": "Sm", "key": "2A62", "mappings": {"default": {"default": "or con doppia barra sopra"}}}, {"category": "Sm", "key": "2A63", "mappings": {"default": {"default": "or con doppia barra sotto"}}}, {"category": "Sm", "key": "2A64", "mappings": {"default": {"default": "antirestrizione di dominio a notazione z"}}}, {"category": "Sm", "key": "2A65", "mappings": {"default": {"default": "antirestrizione di range a notazione z"}}}, {"category": "Sm", "key": "2A66", "mappings": {"default": {"default": "segno di uguale con punto sotto"}}}, {"category": "Sm", "key": "2A67", "mappings": {"default": {"default": "identico con punto sopra"}}}, {"category": "Sm", "key": "2A68", "mappings": {"default": {"default": "tripla barra orizzontale con doppio tratto verticale"}}}, {"category": "Sm", "key": "2A69", "mappings": {"default": {"default": "tripla barra orizzontale con triplo tratto verticale"}}}, {"key": "2A6A", "mappings": {"default": {"default": "tilde con punto sopra"}}, "category": "Sm"}, {"category": "Sm", "key": "2A6B", "mappings": {"default": {"default": "tilde con punti in crescendo"}}}, {"category": "Sm", "key": "2A6C", "mappings": {"default": {"default": "simile meno simile"}}}, {"key": "2A6D", "mappings": {"default": {"default": "congruente con punto sopra"}}, "category": "Sm"}, {"category": "Sm", "key": "2A6E", "mappings": {"default": {"default": "uguale con asterisco"}}}, {"key": "2A6F", "mappings": {"default": {"default": "circa uguale con cappuccio"}}, "category": "Sm"}, {"category": "Sm", "key": "2A70", "mappings": {"default": {"default": "approssimativamente uguale o uguale a"}}}, {"key": "2A71", "mappings": {"default": {"default": "uguale con più sotto"}}, "category": "Sm"}, {"key": "2A72", "mappings": {"default": {"default": "uguale con più sopra"}}, "category": "Sm"}, {"category": "Sm", "key": "2A73", "mappings": {"default": {"default": "uguale sopra tilde"}}}, {"category": "Sm", "key": "2A74", "mappings": {"default": {"default": "doppi due punti uguale"}}}, {"category": "Sm", "key": "2A75", "mappings": {"default": {"default": "due uguali consecutivi"}}}, {"category": "Sm", "key": "2A76", "mappings": {"default": {"default": "tre uguali consecutivi"}}}, {"key": "2A77", "mappings": {"default": {"default": "segno di uguale con due punti sopra e due punti sotto"}}, "category": "Sm"}, {"key": "2A78", "mappings": {"default": {"default": "equivalente con quattro punti sopra"}}, "category": "Sm"}, {"key": "2A79", "mappings": {"default": {"default": "minore di con un cerchio all'interno"}}, "category": "Sm"}, {"category": "Sm", "key": "2A7A", "mappings": {"default": {"default": "maggiore di con cerchio all'interno"}}}, {"category": "Sm", "key": "2A7B", "mappings": {"default": {"default": "minore di con punto interrogativo sopra"}}}, {"category": "Sm", "key": "2A7C", "mappings": {"default": {"default": "maggiore di con punto interrogativo sopra"}}}, {"category": "Sm", "key": "2A7D", "mappings": {"default": {"default": "minore o uguale a inclinato"}}}, {"category": "Sm", "key": "2A7E", "mappings": {"default": {"default": "maggiore o uguale a inclinato"}}}, {"category": "Sm", "key": "2A7F", "mappings": {"default": {"default": "minore o uguale a inclinato con punto dentro"}}}, {"category": "Sm", "key": "2A80", "mappings": {"default": {"default": "maggiore o uguale a inclinato con punto dentro"}}}, {"category": "Sm", "key": "2A81", "mappings": {"default": {"default": "minore o uguale a inclinato con punto sopra"}}}, {"category": "Sm", "key": "2A82", "mappings": {"default": {"default": "maggiore o uguale a inclinato con punto sopra"}}}, {"category": "Sm", "key": "2A83", "mappings": {"default": {"default": "minore o uguale a inclinato con punto sopra a destra"}}}, {"category": "Sm", "key": "2A84", "mappings": {"default": {"default": "maggiore o uguale a inclinato con punto sopra a destra"}}}, {"category": "Sm", "key": "2A85", "mappings": {"default": {"default": "minore di o approssimativo"}}}, {"category": "Sm", "key": "2A86", "mappings": {"default": {"default": "maggiore di o approssimativo"}}}, {"category": "Sm", "key": "2A87", "mappings": {"default": {"default": "minore e singola linea non uguale a"}}}, {"category": "Sm", "key": "2A88", "mappings": {"default": {"default": "maggiore e singola linea non uguale a"}}}, {"category": "Sm", "key": "2A89", "mappings": {"default": {"default": "minore e non approssimativo"}}}, {"category": "Sm", "key": "2A8A", "mappings": {"default": {"default": "maggiore di e non approssimativo"}}}, {"category": "Sm", "key": "2A8B", "mappings": {"default": {"default": "minore di sopra a doppia linea di uguale sopra a maggiore di"}}}, {"category": "Sm", "key": "2A8C", "mappings": {"default": {"default": "maggiore di sopra a doppia linea di uguale sopra a meno di"}}}, {"category": "Sm", "key": "2A8D", "mappings": {"default": {"default": "minore di sopra a simile o uguale"}}}, {"category": "Sm", "key": "2A8E", "mappings": {"default": {"default": "maggiore di sopra simile o uguale"}}}, {"category": "Sm", "key": "2A8F", "mappings": {"default": {"default": "minore di sopra simile sopra maggiore di"}}}, {"category": "Sm", "key": "2A90", "mappings": {"default": {"default": "maggiore di quanto sopra simile sopra minore di"}}}, {"category": "Sm", "key": "2A91", "mappings": {"default": {"default": "minore di sopra maggiore di sopra doppia linea di uguale"}}}, {"category": "Sm", "key": "2A92", "mappings": {"default": {"default": "maggiore di sopra minore di sopra doppia linea di uguale"}}}, {"category": "Sm", "key": "2A93", "mappings": {"default": {"default": "minore di sopra uguale inclinato  sopra maggiore di sopra uguale inclinato"}}}, {"category": "Sm", "key": "2A94", "mappings": {"default": {"default": "maggiore di sopra uguale inclinato sopra minore di sopra uguale inclinato"}}}, {"category": "Sm", "key": "2A95", "mappings": {"default": {"default": "uguale inclinato o minore di"}}}, {"category": "Sm", "key": "2A96", "mappings": {"default": {"default": "uguale inclinato o maggiore di"}}}, {"category": "Sm", "key": "2A97", "mappings": {"default": {"default": "uguale inclinato o minore di con punto dentro"}}}, {"category": "Sm", "key": "2A98", "mappings": {"default": {"default": "uguale inclinato o maggiore di con punto dentro"}}}, {"category": "Sm", "key": "2A99", "mappings": {"default": {"default": "doppia linea uguale o minore di"}}}, {"category": "Sm", "key": "2A9A", "mappings": {"default": {"default": "doppia linea uguale o maggiore di"}}}, {"category": "Sm", "key": "2A9B", "mappings": {"default": {"default": "linea doppia inclinata di uguale o inferiore a"}}}, {"category": "Sm", "key": "2A9C", "mappings": {"default": {"default": "linea doppia inclinata di uguale o maggiore di"}}}, {"category": "Sm", "key": "2A9D", "mappings": {"default": {"default": "simile o minore di"}}}, {"category": "Sm", "key": "2A9E", "mappings": {"default": {"default": "simile o maggiore di"}}}, {"category": "Sm", "key": "2A9F", "mappings": {"default": {"default": "simile sopra minore di sopra uguale"}}}, {"category": "Sm", "key": "2AA0", "mappings": {"default": {"default": "simile sopra maggiore di sopra uguale"}}}, {"category": "Sm", "key": "2AA1", "mappings": {"default": {"default": "doppio minore di uno dentro l'altro"}}}, {"category": "Sm", "key": "2AA2", "mappings": {"default": {"default": "doppio maggiore di uno dentro l'altro"}}}, {"category": "Sm", "key": "2AA3", "mappings": {"default": {"default": "doppio minore di uno dentro l'altro con barra sotto"}}}, {"category": "Sm", "key": "2AA4", "mappings": {"default": {"default": "maggiore di sovrapposto a minore di"}}}, {"category": "Sm", "key": "2AA5", "mappings": {"default": {"default": "maggiore di di fianco a minore di"}}}, {"category": "Sm", "key": "2AA6", "mappings": {"default": {"default": "meno di quanto chiuso da curva"}}}, {"category": "Sm", "key": "2AA7", "mappings": {"default": {"default": "maggiore di chiuso da curva"}}}, {"category": "Sm", "key": "2AA8", "mappings": {"default": {"default": "minore di chiuso per curva sopra uguale inclinato"}}}, {"category": "Sm", "key": "2AA9", "mappings": {"default": {"default": "maggiore di chiuso per curva sopra uguale inclinato"}}}, {"category": "Sm", "key": "2AAA", "mappings": {"default": {"default": "minore di"}}}, {"category": "Sm", "key": "2AAB", "mappings": {"default": {"default": "maggiore di"}}}, {"category": "Sm", "key": "2AAC", "mappings": {"default": {"default": "minore di o uguale a"}}}, {"category": "Sm", "key": "2AAD", "mappings": {"default": {"default": "maggiore o uguale a"}}}, {"category": "Sm", "key": "2AAE", "mappings": {"default": {"default": "segno di uguale con dosso sopra"}}}, {"category": "Sm", "key": "2AAF", "mappings": {"default": {"default": "precede sopra uguale a a linea singola"}}}, {"category": "Sm", "key": "2AB0", "mappings": {"default": {"default": "succede sopra uguale a linea singola"}}}, {"category": "Sm", "key": "2AB1", "mappings": {"default": {"default": "precede sopra diverso da a linea singola"}}}, {"category": "Sm", "key": "2AB2", "mappings": {"default": {"default": "succede sopra diverso da a linea singola"}}}, {"category": "Sm", "key": "2AB3", "mappings": {"default": {"default": "precede sopra uguale"}}}, {"category": "Sm", "key": "2AB4", "mappings": {"default": {"default": "succede sopra uguale"}}}, {"category": "Sm", "key": "2AB5", "mappings": {"default": {"default": "precede sopra non uguale a"}}}, {"category": "Sm", "key": "2AB6", "mappings": {"default": {"default": "riesce al di sopra di non uguale a"}}}, {"category": "Sm", "key": "2AB7", "mappings": {"default": {"default": "precede in alto quasi uguale a"}}}, {"category": "Sm", "key": "2AB8", "mappings": {"default": {"default": "riesce al di sopra di quasi uguale"}}}, {"category": "Sm", "key": "2AB9", "mappings": {"default": {"default": "precede sopra non quasi uguale a"}}}, {"category": "Sm", "key": "2ABA", "mappings": {"default": {"default": "riesce al di sopra di quasi uguale a"}}}, {"category": "Sm", "key": "2ABB", "mappings": {"default": {"default": "do<PERSON><PERSON> precede"}}}, {"category": "Sm", "key": "2ABC", "mappings": {"default": {"default": "doppio succede"}}}, {"category": "Sm", "key": "2ABD", "mappings": {"default": {"default": "sottoinsieme con punto"}}}, {"category": "Sm", "key": "2ABE", "mappings": {"default": {"default": "superinsieme con punto"}}}, {"category": "Sm", "key": "2ABF", "mappings": {"default": {"default": "sottoinsieme con segno più sotto"}}}, {"category": "Sm", "key": "2AC0", "mappings": {"default": {"default": "superinsieme con segno più sotto"}}}, {"category": "Sm", "key": "2AC1", "mappings": {"default": {"default": "sottoinsieme con segno di moltiplicazione sotto"}}}, {"category": "Sm", "key": "2AC2", "mappings": {"default": {"default": "superset con segno di moltiplicazione sotto"}}}, {"category": "Sm", "key": "2AC3", "mappings": {"default": {"default": "sottoinsieme di o uguale a con punto sopra"}}}, {"category": "Sm", "key": "2AC4", "mappings": {"default": {"default": "superinsieme o uguale a con punto sopra"}}}, {"category": "Sm", "key": "2AC5", "mappings": {"default": {"default": "sottoinsieme di sopra uguale"}}}, {"category": "Sm", "key": "2AC6", "mappings": {"default": {"default": "superinsieme di sopra uguale"}}}, {"category": "Sm", "key": "2AC7", "mappings": {"default": {"default": "sottoinsieme di sopra tilde"}}}, {"category": "Sm", "key": "2AC8", "mappings": {"default": {"default": "superset di Above Tilde Operator"}}}, {"category": "Sm", "key": "2AC9", "mappings": {"default": {"default": "sottoinsieme di sopra quasi uguale a"}}}, {"category": "Sm", "key": "2ACA", "mappings": {"default": {"default": "superinsieme di sopra quasi uguale a"}}}, {"category": "Sm", "key": "2ACB", "mappings": {"default": {"default": "sottoinsieme di sopra a non uguale a"}}}, {"category": "Sm", "key": "2ACC", "mappings": {"default": {"default": "superinsieme di sopra a non uguale a"}}}, {"category": "Sm", "key": "2ACD", "mappings": {"default": {"default": "operatore box quadrato aperto a sinistra"}}}, {"category": "Sm", "key": "2ACE", "mappings": {"default": {"default": "operatore box quadrato aperto a destra"}}}, {"category": "Sm", "key": "2ACF", "mappings": {"default": {"default": "sottoinsieme chiuso"}}}, {"category": "Sm", "key": "2AD0", "mappings": {"default": {"default": "superinsieme chiuso"}}}, {"category": "Sm", "key": "2AD1", "mappings": {"default": {"default": "sottoinsieme chiuso o uguale a"}}}, {"category": "Sm", "key": "2AD2", "mappings": {"default": {"default": "superinsieme chiuso o uguale a"}}}, {"category": "Sm", "key": "2AD3", "mappings": {"default": {"default": "sottoinsieme sopra superinsieme"}}}, {"category": "Sm", "key": "2AD4", "mappings": {"default": {"default": "superinsieme sopra sottoinsieme"}}}, {"category": "Sm", "key": "2AD5", "mappings": {"default": {"default": "sottoinsieme sopra sottoinsieme"}}}, {"category": "Sm", "key": "2AD6", "mappings": {"default": {"default": "superinsieme sopra superinsieme"}}}, {"category": "Sm", "key": "2AD7", "mappings": {"default": {"default": "superinsieme accanto a sottoinsieme"}}}, {"category": "Sm", "key": "2AD8", "mappings": {"default": {"default": "superinsieme accanto a e unito con trattino a sottoinsieme"}}}, {"category": "Sm", "key": "2AD9", "mappings": {"default": {"default": "elemento di apertura verso il basso"}}}, {"category": "Sm", "key": "2ADA", "mappings": {"default": {"default": "forcone con top a T"}}}, {"category": "Sm", "key": "2ADB", "mappings": {"default": {"default": "intersezione trasversale"}}}, {"category": "Sm", "key": "2ADC", "mappings": {"default": {"default": "biforcazione "}}}, {"category": "Sm", "key": "2ADD", "mappings": {"default": {"default": "nonforking"}}}, {"category": "Sm", "key": "2ADE", "mappings": {"default": {"default": "tornello corto a sinistra"}}}, {"category": "Sm", "key": "2ADF", "mappings": {"default": {"default": "tornello corto verso il basso"}}}, {"category": "Sm", "key": "2AE0", "mappings": {"default": {"default": "tornello corto verso l'alto"}}}, {"category": "Sm", "key": "2AE1", "mappings": {"default": {"default": "perpendicolare con S"}}}, {"category": "Sm", "key": "2AE2", "mappings": {"default": {"default": "tornello a destra triplo a barra verticale"}}}, {"category": "Sm", "key": "2AE3", "mappings": {"default": {"default": "doppia barra verticale con tornello a sinistra"}}}, {"category": "Sm", "key": "2AE4", "mappings": {"default": {"default": "tornello a sinistra doppio a barra verticale"}}}, {"category": "Sm", "key": "2AE5", "mappings": {"default": {"default": "doppia barra verticale con doppio tornello a sinistra"}}}, {"category": "Sm", "key": "2AE6", "mappings": {"default": {"default": "lineetta lunga che parte da elemento sinistro di doppia barra vericale"}}}, {"category": "Sm", "key": "2AE7", "mappings": {"default": {"default": "tornello verso il basso corto con barra sopra"}}}, {"category": "Sm", "key": "2AE8", "mappings": {"default": {"default": "tornello verso l'alto corto con barra sotto"}}}, {"category": "Sm", "key": "2AE9", "mappings": {"default": {"default": "tornello verso l'alto corto sopra tornello verso il basso corto"}}}, {"category": "Sm", "key": "2AEA", "mappings": {"default": {"default": "doppio tornello vero il basso"}}}, {"category": "Sm", "key": "2AEB", "mappings": {"default": {"default": "doppio tornello verso l'alto"}}}, {"category": "Sm", "key": "2AEC", "mappings": {"default": {"default": "segno di not con doppia linea orizzontale"}}}, {"category": "Sm", "key": "2AED", "mappings": {"default": {"default": "segno di not con doppia linea orizzontale invertito"}}}, {"category": "Sm", "key": "2AEE", "mappings": {"default": {"default": "non divide con barra di negazione inversa"}}}, {"category": "Sm", "key": "2AEF", "mappings": {"default": {"default": "linea verticale con cerchio sopra"}}}, {"category": "Sm", "key": "2AF0", "mappings": {"default": {"default": "linea verticale con cerchio sotto"}}}, {"category": "Sm", "key": "2AF1", "mappings": {"default": {"default": "tornello verso il basso con cerchio sotto"}}}, {"category": "Sm", "key": "2AF2", "mappings": {"default": {"default": "parallelo con tratto orizzontale"}}}, {"category": "Sm", "key": "2AF3", "mappings": {"default": {"default": "parallelo con tilde"}}}, {"category": "Sm", "key": "2AF4", "mappings": {"default": {"default": "relazione binaria a tripla barra verticale"}}}, {"category": "Sm", "key": "2AF5", "mappings": {"default": {"default": "tripla barra verticale con tratto orizzontale"}}}, {"category": "Sm", "key": "2AF6", "mappings": {"default": {"default": "operatore di tripli punti"}}}, {"category": "Sm", "key": "2AF7", "mappings": {"default": {"default": "tra minore di uno dentro l'altro"}}}, {"category": "Sm", "key": "2AF8", "mappings": {"default": {"default": "tra maggiore di uno dentro l'altro"}}}, {"category": "Sm", "key": "2AF9", "mappings": {"default": {"default": "minore o uguale a con doppia linea inclinata"}}}, {"category": "Sm", "key": "2AFA", "mappings": {"default": {"default": "maggiore o uguale a con doppia linea inclinata"}}}, {"category": "Sm", "key": "2AFB", "mappings": {"default": {"default": "triplice slash"}}}, {"category": "Sm", "key": "2AFC", "mappings": {"default": {"default": "operatore barre verticali triplo"}}}, {"category": "Sm", "key": "2AFD", "mappings": {"default": {"default": "doppio slash"}}}, {"category": "Sm", "key": "2AFE", "mappings": {"default": {"default": "barra verticale bianca"}}}, {"category": "Sm", "key": "2AFF", "mappings": {"default": {"default": "barra verticale bianca"}}}, {"category": "Pd", "key": "301C", "mappings": {"default": {"default": "t<PERSON><PERSON> ondu<PERSON>o"}}}, {"category": "Po", "key": "FE10", "mappings": {"default": {"default": "modulo di presentazione per Virgola verticale"}}}, {"category": "Po", "key": "FE13", "mappings": {"default": {"default": "modulo di presentazione per due punti verticale"}}}, {"category": "Po", "key": "FE14", "mappings": {"default": {"default": "modulo di presentazione per punto e virgola verticale"}}}, {"category": "Po", "key": "FE15", "mappings": {"default": {"default": "modulo di presentazione per il punto esclamativo verticale"}}}, {"category": "Po", "key": "FE16", "mappings": {"default": {"default": "modulo di presentazione per il punto interrogativo verticale"}}}, {"category": "Po", "key": "FE19", "mappings": {"default": {"default": "modulo di presentazione per l'ellisse orizzontale verticale"}}}, {"category": "Po", "key": "FE30", "mappings": {"default": {"default": "glifo di pue punti verticali"}}}, {"category": "Pd", "key": "FE31", "mappings": {"default": {"default": "glifo di lineetta emme verticale"}}}, {"category": "Pd", "key": "FE32", "mappings": {"default": {"default": "glifo di lineetta enne verticale"}}}, {"category": "Pc", "key": "FE33", "mappings": {"default": {"default": "glifo di sottolineatura verticale"}}}, {"category": "Pc", "key": "FE34", "mappings": {"default": {"default": "glifo di sottolineatura verticale ondulata"}}}, {"category": "Po", "key": "FE45", "mappings": {"default": {"default": "punto sesamo"}}}, {"category": "Po", "key": "FE46", "mappings": {"default": {"default": "punto sesamo bianco"}}}, {"category": "Po", "key": "FE49", "mappings": {"default": {"default": "tratto in alto tratteggiato"}}}, {"category": "Po", "key": "FE4A", "mappings": {"default": {"default": "due trattini con spazio in alto"}}}, {"category": "Po", "key": "FE4B", "mappings": {"default": {"default": "linea ondulata"}}}, {"category": "Po", "key": "FE4C", "mappings": {"default": {"default": "linea ondulata doppia"}}}, {"category": "Pc", "key": "FE4D", "mappings": {"default": {"default": "barra sottra tratteggiata"}}}, {"category": "Pc", "key": "FE4E", "mappings": {"default": {"default": "due trattini con spazio in basso"}}}, {"category": "Pc", "key": "FE4F", "mappings": {"default": {"default": "linea ondulata"}}}, {"category": "Po", "key": "FE50", "mappings": {"default": {"default": "virgola piccola"}}}, {"category": "Po", "key": "FE52", "mappings": {"default": {"default": "punto piccolo"}}}, {"category": "Po", "key": "FE54", "mappings": {"default": {"default": "punto e virgola piccolo"}}}, {"category": "Po", "key": "FE55", "mappings": {"default": {"default": "due punti piccoli"}}}, {"category": "Po", "key": "FE56", "mappings": {"default": {"default": "punto interrogativo piccolo"}}}, {"category": "Po", "key": "FE57", "mappings": {"default": {"default": "punto esclamativo piccolo"}}}, {"category": "Pd", "key": "FE58", "mappings": {"default": {"default": "lineetta emme piccola"}}}, {"category": "Po", "key": "FE5F", "mappings": {"default": {"default": "segno di numero piccolo"}}}, {"category": "Po", "key": "FE60", "mappings": {"default": {"default": "e commerciale piccola"}}}, {"category": "Po", "key": "FE61", "mappings": {"default": {"default": "asterisco piccolo"}}}, {"category": "Sm", "key": "FE62", "mappings": {"default": {"default": "segno più piccolo"}}}, {"category": "Pd", "key": "FE63", "mappings": {"default": {"default": "trattino-meno piccolo"}}}, {"category": "Sm", "key": "FE64", "mappings": {"default": {"default": "segno minore di piccolo"}}}, {"category": "Sm", "key": "FE65", "mappings": {"default": {"default": "segno maggiore di piccolo"}}}, {"category": "Sm", "key": "FE66", "mappings": {"default": {"default": "segno di uguale piccolo"}}}, {"category": "Po", "key": "FE68", "mappings": {"default": {"default": "solido inverso piccolo"}}}, {"category": "Sc", "key": "FE69", "mappings": {"default": {"default": "simbolo del dollaro piccolo"}}}, {"category": "Po", "key": "FE6A", "mappings": {"default": {"default": "segno percentuale piccolo"}}}, {"category": "Po", "key": "FE6B", "mappings": {"default": {"default": "commerciale a piccola"}}}, {"category": "Po", "key": "FF01", "mappings": {"default": {"default": "punto esclamativo fullwidth"}}}, {"category": "Po", "key": "FF02", "mappings": {"default": {"default": "virgolette fullwidth"}}}, {"category": "Po", "key": "FF03", "mappings": {"default": {"default": "segno di numero fullwidth"}}}, {"category": "Sc", "key": "FF04", "mappings": {"default": {"default": "segno di dollaro fullwidth"}}}, {"category": "Po", "key": "FF05", "mappings": {"default": {"default": "segno di percentuale fullwidth"}}}, {"category": "Po", "key": "FF06", "mappings": {"default": {"default": "e commerciale fullwidth"}}}, {"category": "Po", "key": "FF07", "mappings": {"default": {"default": "apostrofo fullwidth"}}}, {"category": "Po", "key": "FF0A", "mappings": {"default": {"default": "asterisco fullwidth"}}}, {"category": "Sm", "key": "FF0B", "mappings": {"default": {"default": "segno più fullwidth"}}}, {"category": "Po", "key": "FF0C", "mappings": {"default": {"default": "virgola fullwidth"}}}, {"category": "Pd", "key": "FF0D", "mappings": {"default": {"default": "trattino-meno  fullwidth"}}}, {"category": "Po", "key": "FF0E", "mappings": {"default": {"default": "punto fullwidth"}}}, {"category": "Po", "key": "FF0F", "mappings": {"default": {"default": "slash fullwidth"}}}, {"category": "Po", "key": "FF1A", "mappings": {"default": {"default": "due punti fullwidth"}}}, {"category": "Po", "key": "FF1B", "mappings": {"default": {"default": "punto e virgola fullwidth"}}}, {"category": "Sm", "key": "FF1C", "mappings": {"default": {"default": "minore di  fullwidth"}}}, {"category": "Sm", "key": "FF1D", "mappings": {"default": {"default": "uguale a fullwidth"}}}, {"category": "Sm", "key": "FF1E", "mappings": {"default": {"default": "maggiore di fullwidth"}}}, {"category": "Po", "key": "FF1F", "mappings": {"default": {"default": "punto interrogativo fullwidth"}}}, {"category": "Po", "key": "FF20", "mappings": {"default": {"default": "a commerciale fullwidth"}}}, {"category": "Po", "key": "FF3C", "mappings": {"default": {"default": "backslash fullwidth"}}}, {"category": "Sk", "key": "FF3E", "mappings": {"default": {"default": "accento circonflesso fullwidth"}}}, {"category": "Pc", "key": "FF3F", "mappings": {"default": {"default": "barra bassa fullwidth"}}}, {"category": "Sk", "key": "FF40", "mappings": {"default": {"default": "accento grave fullwidth"}}}, {"category": "Sm", "key": "FF5C", "mappings": {"default": {"default": "linea verticale fullwidth"}}}, {"category": "Sm", "key": "FF5E", "mappings": {"default": {"default": "tilde fullwidth"}}}, {"category": "Sc", "key": "FFE0", "mappings": {"default": {"default": "cent fullwidth"}}}, {"category": "Sc", "key": "FFE1", "mappings": {"default": {"default": "segno pound fullwidth"}}}, {"category": "Sm", "key": "FFE2", "mappings": {"default": {"default": "segno not fullwidth"}}}, {"category": "Sk", "key": "FFE3", "mappings": {"default": {"default": "macron fullwidth"}}}, {"category": "So", "key": "FFE4", "mappings": {"default": {"default": "barra verticale spezzata fullwidth"}}}, {"category": "Sc", "key": "FFE5", "mappings": {"default": {"default": "segno yes fullwidth"}}}, {"category": "Sc", "key": "FFE6", "mappings": {"default": {"default": "<PERSON><PERSON> won fullwidth"}}}, {"category": "So", "key": "FFE8", "mappings": {"default": {"default": "barra verticale halfwidth"}}}, {"category": "So", "key": "FFED", "mappings": {"default": {"default": "quadrato nero halfwidth"}}}, {"category": "So", "key": "FFEE", "mappings": {"default": {"default": "quadrato bianco halfwidth"}}}], "it/symbols/math_whitespace.min": [{"locale": "it"}, {"category": "Zs", "key": "0020", "mappings": {"default": {"default": "spazio"}}}, {"key": "00A0", "mappings": {"default": {"default": " "}}, "category": "Zs"}, {"category": "Cf", "key": "00AD", "mappings": {"default": {"default": "trattino discrezionale"}}}, {"category": "Zs", "key": "2000", "mappings": {"default": {"default": "in quad"}}}, {"category": "Zs", "key": "2001", "mappings": {"default": {"default": "montone quad"}}}, {"key": "2002", "mappings": {"default": {"default": ""}}, "category": "Zs"}, {"key": "2003", "mappings": {"default": {"default": ""}}, "category": "Zs"}, {"key": "2004", "mappings": {"default": {"default": ""}}, "category": "Zs"}, {"key": "2005", "mappings": {"default": {"default": ""}}, "category": "Zs"}, {"category": "Zs", "key": "2006", "mappings": {"default": {"default": "spazio per sei persone"}}}, {"key": "2007", "mappings": {"default": {"default": ""}}, "category": "Zs"}, {"key": "2008", "mappings": {"default": {"default": ""}}, "category": "Zs"}, {"key": "2009", "mappings": {"default": {"default": ""}}, "category": "Zs"}, {"key": "200A", "mappings": {"default": {"default": ""}}, "category": "Zs"}, {"key": "200B", "mappings": {"default": {"default": ""}}, "category": "Cf"}, {"category": "Cf", "key": "200C", "mappings": {"default": {"default": "zero Width Non-Joiner"}}}, {"category": "Cf", "key": "200D", "mappings": {"default": {"default": "zero Width Joiner"}}}, {"category": "Cf", "key": "200E", "mappings": {"default": {"default": "marchio da sinistra a destra"}}}, {"category": "Cf", "key": "200F", "mappings": {"default": {"default": "marchio da destra a sinistra"}}}, {"category": "Zl", "key": "2028", "mappings": {"default": {"default": "separatore di righe"}}}, {"category": "Zp", "key": "2029", "mappings": {"default": {"default": "separatore di paragrafi"}}}, {"category": "Cf", "key": "202A", "mappings": {"default": {"default": "incorporamento da sinistra a destra"}}}, {"category": "Cf", "key": "202B", "mappings": {"default": {"default": "incorporamento da destra a sinistra"}}}, {"category": "Cf", "key": "202C", "mappings": {"default": {"default": "pop formattazione direzionale"}}}, {"category": "Cf", "key": "202D", "mappings": {"default": {"default": "override da sinistra a destra"}}}, {"category": "Cf", "key": "202E", "mappings": {"default": {"default": "override da destra a sinistra"}}}, {"category": "Zs", "key": "202F", "mappings": {"default": {"default": "spazio indivisibile piccolo"}}}, {"key": "205F", "mappings": {"default": {"default": ""}}, "category": "Zs"}, {"key": "2060", "mappings": {"default": {"default": ""}}, "category": "Cf"}, {"key": "2061", "mappings": {"default": {"default": "di"}}, "category": "Cf"}, {"key": "2062", "mappings": {"default": {"default": "per"}}, "category": "Cf"}, {"key": "2063", "mappings": {"default": {"default": "virgola"}}, "category": "Cf"}, {"category": "Cf", "key": "2064", "mappings": {"default": {"default": "più"}}}, {"category": "Cf", "key": "206A", "mappings": {"default": {"default": "inibire lo scambio simmetrico"}}}, {"category": "Cf", "key": "206B", "mappings": {"default": {"default": "attiva lo scambio simmetrico"}}}, {"category": "Cf", "key": "206E", "mappings": {"default": {"default": "forme di cifre nazionali"}}}, {"category": "Cf", "key": "206F", "mappings": {"default": {"default": "forme di cifre nominali"}}}, {"key": "FEFF", "mappings": {"default": {"default": ""}}, "category": "Cf"}, {"category": "Cf", "key": "FFF9", "mappings": {"default": {"default": "ancora di annotazione interlineare"}}}, {"category": "Cf", "key": "FFFA", "mappings": {"default": {"default": "separatore di annotazioni interlineari"}}}, {"category": "Cf", "key": "FFFB", "mappings": {"default": {"default": "terminatore di annotazione interlineare"}}}], "it/symbols/other_stars.min": [{"locale": "it"}, {"category": "So", "key": "23E8", "mappings": {"default": {"default": "simbolo decimale degli esponenti"}}}, {"category": "So", "key": "2605", "mappings": {"default": {"default": "stella nera"}}}, {"category": "So", "key": "2606", "mappings": {"default": {"default": "stella bianca"}}}, {"category": "So", "key": "26AA", "mappings": {"default": {"default": "cerchio bianco medio"}}}, {"category": "So", "key": "26AB", "mappings": {"default": {"default": "cerchio nero medio"}}}, {"category": "So", "key": "2705", "mappings": {"default": {"default": "bianco pesante segno di spunta"}}}, {"category": "So", "key": "2713", "mappings": {"default": {"default": "segno di spunta"}}}, {"category": "So", "key": "2714", "mappings": {"default": {"default": "segno di spunta pesante"}}}, {"category": "So", "key": "2715", "mappings": {"default": {"default": "moltiplicazione X"}}}, {"category": "So", "key": "2716", "mappings": {"default": {"default": "moltiplicazione pesante X"}}}, {"category": "So", "key": "2717", "mappings": {"default": {"default": "ballot X"}}}, {"category": "So", "key": "2718", "mappings": {"default": {"default": "ballot X pesante"}}}, {"category": "So", "key": "271B", "mappings": {"default": {"default": "croce centrale aperta"}}}, {"category": "So", "key": "271C", "mappings": {"default": {"default": "croce centrale aperta grassetto"}}}, {"key": "2720", "mappings": {"default": {"default": "croce maltese"}}, "category": "So"}, {"category": "So", "key": "2721", "mappings": {"default": {"default": "stella di David<PERSON>"}}}, {"category": "So", "key": "2722", "mappings": {"default": {"default": "quattro asteroidi a lacrima"}}}, {"category": "So", "key": "2723", "mappings": {"default": {"default": "quattro asterischi con palloncino"}}}, {"category": "So", "key": "2724", "mappings": {"default": {"default": "pesante quattro asterischi con palloncino"}}}, {"category": "So", "key": "2725", "mappings": {"default": {"default": "quattro asteroidi randelli"}}}, {"category": "So", "key": "2726", "mappings": {"default": {"default": "stella nera a quattro punte"}}}, {"category": "So", "key": "2727", "mappings": {"default": {"default": "stella bianca a quattro punte"}}}, {"category": "So", "key": "2728", "mappings": {"default": {"default": "bagliore"}}}, {"category": "So", "key": "2729", "mappings": {"default": {"default": "sottolineato stella bianca"}}}, {"category": "So", "key": "272A", "mappings": {"default": {"default": "stella bianca cerchiata"}}}, {"category": "So", "key": "272B", "mappings": {"default": {"default": "stella bianca centrale aperta"}}}, {"category": "So", "key": "272C", "mappings": {"default": {"default": "stella bianca centrale nero"}}}, {"category": "So", "key": "272D", "mappings": {"default": {"default": "stella nera delineato"}}}, {"category": "So", "key": "272E", "mappings": {"default": {"default": "stella nera profilata pesante"}}}, {"category": "So", "key": "272F", "mappings": {"default": {"default": "stella girandola"}}}, {"category": "So", "key": "2730", "mappings": {"default": {"default": "stella bianca ombreggiata"}}}, {"category": "So", "key": "2731", "mappings": {"default": {"default": "asterisco pesante"}}}, {"category": "So", "key": "2732", "mappings": {"default": {"default": "asterisco centrale aperta"}}}, {"category": "So", "key": "2733", "mappings": {"default": {"default": "asterisco a otto punte"}}}, {"category": "So", "key": "2734", "mappings": {"default": {"default": "stella nera a otto punte"}}}, {"category": "So", "key": "2735", "mappings": {"default": {"default": "stella a girandola a otto punte"}}}, {"key": "2736", "mappings": {"default": {"default": "stella nera a sei punte"}}, "category": "So"}, {"category": "So", "key": "2739", "mappings": {"default": {"default": "stella nera a dodici punte"}}}, {"category": "So", "key": "273A", "mappings": {"default": {"default": "asterisco a sedici punte"}}}, {"category": "So", "key": "273B", "mappings": {"default": {"default": "asterisco a lacrima"}}}, {"category": "So", "key": "273C", "mappings": {"default": {"default": "asterisco a forma di lacrima del centro aperto"}}}, {"category": "So", "key": "273D", "mappings": {"default": {"default": "asterisco pesante a forma di lacrima"}}}, {"category": "So", "key": "273E", "mappings": {"default": {"default": "sei petali in bianco e nero Florette"}}}, {"category": "So", "key": "273F", "mappings": {"default": {"default": "black Florette"}}}, {"category": "So", "key": "2740", "mappings": {"default": {"default": "florette bianco"}}}, {"category": "So", "key": "2741", "mappings": {"default": {"default": "florette nera a otto petali profilata"}}}, {"category": "So", "key": "2742", "mappings": {"default": {"default": "stella a otto punte con centro aperto cerchiato"}}}, {"category": "So", "key": "2743", "mappings": {"default": {"default": "asterisco a forma di girandola pesante a goccia"}}}, {"category": "So", "key": "2744", "mappings": {"default": {"default": "fiocco di neve"}}}, {"category": "So", "key": "2745", "mappings": {"default": {"default": "fiocco di neve Trifoliate stretto"}}}, {"category": "So", "key": "2746", "mappings": {"default": {"default": "fiocco di neve con chevron"}}}, {"category": "So", "key": "2747", "mappings": {"default": {"default": "scintillare"}}}, {"category": "So", "key": "2748", "mappings": {"default": {"default": "sparkle pesante"}}}, {"category": "So", "key": "2749", "mappings": {"default": {"default": "asterisco con palloncino"}}}, {"category": "So", "key": "274A", "mappings": {"default": {"default": "otto asterisco a elica a forma di lacrima"}}}, {"category": "So", "key": "274B", "mappings": {"default": {"default": "asterisco a elica a otto squarcia a forma di lacrima"}}}, {"category": "So", "key": "274C", "mappings": {"default": {"default": "segno di croce"}}}, {"category": "So", "key": "274D", "mappings": {"default": {"default": "cerchio bianco o<PERSON>"}}}], "it/units/area.min": [{"locale": "it"}, {"locale": "it"}, {"key": "sq", "category": "other", "names": ["sq", "sq."], "mappings": {"default": {"plural": "quadrati", "default": "quadrato"}}}, {"key": "sq in", "mappings": {"default": {"plural": "pollici quadrati", "default": "pollice quadrato"}}, "category": "area", "names": ["sq in", "sq. in.", "sq inch", "sq. inch"]}, {"key": "sq rd", "category": "area", "names": ["sq rd", "sq. rd."], "mappings": {"default": {"plural": "per<PERSON><PERSON> quadrati", "default": "pertica quadrata"}}}, {"key": "sq ft", "mappings": {"default": {"plural": "piedi quadrati", "default": "piede quadrato"}}, "category": "area", "names": ["sq ft", "sq. ft."]}, {"key": "sq yd", "mappings": {"default": {"plural": "iarde quadrate", "default": "iarda quadrata"}}, "category": "area", "names": ["sq yd", "sq. yd."]}, {"key": "sq mi", "mappings": {"default": {"plural": "miglia quadrate", "default": "miglio quadrato"}}, "category": "area", "names": ["sq mi", "sq. mi."]}, {"key": "ac", "mappings": {"default": {"plural": "acri", "default": "acro"}}, "category": "area", "names": ["ac", "ac.", "acr", "acr."]}, {"key": "ha", "mappings": {"default": {"plural": "ettari", "default": "ettaro"}}, "category": "area", "names": ["ha"]}], "it/units/currency.min": [{"locale": "it"}, {"key": "$", "mappings": {"default": {"plural": "dollari", "default": "dollaro"}}, "category": "currency", "names": ["$", "💲", "＄", "﹩", "USD"]}, {"key": "£", "mappings": {"default": {"plural": "sterline", "default": "sterlina"}}, "category": "currency", "names": ["£", "￡", "GBP"]}, {"key": "¥", "mappings": {"default": {"plural": "yen", "default": "yen"}}, "category": "currency", "names": ["¥", "￥", "JPY"]}, {"key": "€", "mappings": {"default": {"plural": "euro", "default": "euro"}}, "category": "currency", "names": ["€", "EUR"]}, {"key": "₡", "mappings": {"default": {"plural": "colons", "default": "colón"}}, "category": "currency", "names": ["₡", "CRC"]}, {"key": "₢", "mappings": {"default": {"plural": "c<PERSON><PERSON><PERSON>", "default": "c<PERSON><PERSON><PERSON>"}}, "category": "currency", "names": ["₢"]}, {"key": "₣", "mappings": {"default": {"plural": "franchi", "default": "franco"}}, "category": "currency", "names": ["₣"]}, {"key": "₤", "mappings": {"default": {"plural": "lire", "default": "lira"}}, "category": "currency", "names": ["₤"]}, {"key": "₥", "mappings": {"default": {"plural": "millesimi di dollaro", "default": "millesimo di dollaro"}}, "category": "currency", "names": ["₥"]}, {"key": "₦", "mappings": {"default": {"plural": "naira", "default": "naira"}}, "category": "currency", "names": ["₦", "NGN"]}, {"key": "₧", "mappings": {"default": {"plural": "pesetas", "default": "peseta"}}, "category": "currency", "names": ["₧"]}, {"key": "₨", "mappings": {"default": {"plural": "rupie", "default": "rupia"}}, "category": "currency", "names": ["₨", "₹", "INR", "NPR", "PKR", "LKR"]}, {"key": "₩", "mappings": {"default": {"plural": "won", "default": "won"}}, "category": "currency", "names": ["₩", "￦", "KRW"]}, {"key": "₪", "mappings": {"default": {"plural": "<PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON><PERSON>"}}, "category": "currency", "names": ["₪"]}, {"key": "₫", "mappings": {"default": {"plural": "dong", "default": "dong"}}, "category": "currency", "names": ["₫"]}, {"key": "₭", "mappings": {"default": {"plural": "kip", "default": "kip"}}, "category": "currency", "names": ["₭"]}, {"key": "₮", "mappings": {"default": {"plural": "<PERSON><PERSON>", "default": "<PERSON><PERSON>"}}, "category": "currency", "names": ["₮"]}, {"key": "₯", "mappings": {"default": {"plural": "dracme", "default": "dracma"}}, "category": "currency", "names": ["₯"]}, {"key": "₰", "mappings": {"default": {"plural": "penny tedeschi", "default": "penny tedesco"}}, "category": "currency", "names": ["₰"]}, {"key": "₱", "mappings": {"default": {"plural": "peso", "default": "peso"}}, "category": "currency", "names": ["₱"]}, {"key": "₲", "mappings": {"default": {"plural": "guaranis", "default": "guaranis"}}, "category": "currency", "names": ["₲"]}, {"key": "₳", "mappings": {"default": {"plural": "austral", "default": "austral"}}, "category": "currency", "names": ["₳"]}, {"key": "₴", "mappings": {"default": {"plural": "hryvnias", "default": "hryvnia"}}, "category": "currency", "names": ["₴", "UAH"]}, {"key": "₵", "mappings": {"default": {"plural": "cedis", "default": "cedis"}}, "category": "currency", "names": ["₵", "GHS"]}, {"category": "currency", "key": "₶", "mappings": {"default": {"plural": "livre tournois", "default": "livre tournois"}}, "names": ["₶"]}, {"category": "currency", "key": "₷", "mappings": {"default": {"plural": "spes<PERSON><PERSON>", "default": "spesmilo"}}, "names": ["₷"]}, {"category": "currency", "key": "₸", "mappings": {"default": {"plural": "tenge", "default": "tenge"}}, "names": ["₸", "KZT"]}, {"category": "currency", "key": "₺", "mappings": {"default": {"plural": "lire turche", "default": "lira turca"}}, "names": ["₺", "TRY"]}, {"key": "元", "mappings": {"default": {"plural": "yuan", "default": "yuan"}}, "category": "currency", "names": ["元"]}, {"category": "currency", "key": "¢", "mappings": {"default": {"plural": "centesimi", "default": "centesimo"}}, "names": ["￠", "¢"]}], "it/units/energy.min": [{"locale": "it"}, {"key": "W", "mappings": {"default": {"plural": "watt", "default": "watt"}}, "category": "energy", "names": ["W", "w"], "si": true}, {"key": "kwh", "category": "energy", "names": ["kwh", "kWh"], "mappings": {"default": {"plural": "kilowattore", "default": "kilowattora"}}}, {"key": "J", "mappings": {"default": {"plural": "joule", "default": "joule"}}, "category": "energy", "names": ["J"], "si": true}, {"key": "N", "mappings": {"default": {"plural": "newton", "default": "newton"}}, "category": "energy", "names": ["N"], "si": true}, {"key": "A", "mappings": {"default": {"plural": "ampere", "default": "ampere"}}, "category": "energy", "names": ["A"], "si": true}, {"key": "V", "mappings": {"default": {"plural": "volt", "default": "volt"}}, "category": "energy", "names": ["V"], "si": true}, {"key": "ohm", "category": "energy", "names": ["Ohm", "ohm"], "mappings": {"default": {"plural": "ohm", "default": "ohm"}}}, {"key": "Ω", "mappings": {"default": {"plural": "ohm", "default": "ohm"}}, "category": "energy", "names": ["Ω"], "si": true}], "it/units/length.min": [{"locale": "it"}, {"key": "ft", "mappings": {"default": {"plural": "piedi", "default": "piede"}}, "category": "length", "names": ["ft", "ft."]}, {"key": "in", "mappings": {"default": {"plural": "pollici", "default": "pollice"}}, "category": "length", "names": ["in", "in."]}, {"key": "mi", "mappings": {"default": {"plural": "miglia", "default": "miglio"}}, "category": "length", "names": ["mi", "mi."]}, {"key": "yd", "mappings": {"default": {"plural": "iarde", "default": "iarda"}}, "category": "length", "names": ["yd", "yd."]}, {"key": "li", "mappings": {"default": {"plural": "links", "default": "link"}}, "category": "length", "names": ["li", "li."]}, {"key": "rd", "mappings": {"default": {"plural": "pertiche", "default": "pertica"}}, "category": "length", "names": ["rd", "rd."]}, {"key": "ch", "mappings": {"default": {"plural": "catene", "default": "catena"}}, "category": "length", "names": ["ch", "ch."]}, {"key": "furlong", "category": "length", "names": ["fur", "fur."], "mappings": {"default": {"plural": "furlong", "default": "furlong"}}}, {"key": "n.m.", "category": "length", "names": ["n.m."], "mappings": {"default": {"plural": "miglia nautiche", "default": "miglio nautico"}}}, {"key": "m", "mappings": {"default": {"plural": "metri", "default": "metro"}}, "category": "length", "names": ["m"], "si": true}], "it/units/memory.min": [{"locale": "it"}, {"key": "b", "category": "memory", "names": ["b"], "mappings": {"default": {"plural": "bit", "default": "bit"}}}, {"key": "B", "mappings": {"default": {"plural": "byte", "default": "byte"}}, "category": "memory", "names": ["B"], "si": true}, {"key": "KB", "category": "memory", "names": ["KB"], "mappings": {"default": {"plural": "chilobyte", "default": "chilobyte"}}}], "it/units/other.min": [{"locale": "it"}, {"key": "doz", "category": "other", "names": ["doz", "doz.", "dz", "dz."], "mappings": {"default": {"plural": "dozzine", "default": "<PERSON><PERSON>na"}}}], "it/units/speed.min": [{"locale": "it"}, {"key": "kt", "category": "speed", "names": ["kt", "kt.", "nd"], "mappings": {"default": {"plural": "nodi", "default": "nodo"}}}, {"key": "mph", "mappings": {"default": {"plural": "miglia orarie", "default": "miglio orario"}}, "category": "speed", "names": ["mph"]}, {"key": "rpm", "mappings": {"default": {"plural": "giri al minuto", "default": "giro al minuto"}}, "category": "speed", "names": ["rpm"]}, {"key": "kmh", "category": "speed", "names": ["kmh"], "mappings": {"default": {"plural": "chilometri orari", "default": "chilometro orario"}}}], "it/units/temperature.min": [{"locale": "it"}, {"key": "F", "mappings": {"default": {"plural": "gradi fahrenheit", "default": "grado fahrenheit"}}, "category": "temperature", "names": ["F", "F.", "°F"]}, {"key": "C", "mappings": {"default": {"plural": "gradi celsius", "default": "grado celsius"}}, "category": "temperature", "names": ["C", "°C"]}, {"key": "K", "mappings": {"default": {"plural": "kelvin", "default": "kelvin"}}, "category": "temperature", "names": ["K", "°K"]}], "it/units/time.min": [{"locale": "it"}, {"key": "s", "category": "time", "names": ["s"], "si": true, "mappings": {"default": {"plural": "secondi", "default": "secondo"}}}, {"key": "″", "mappings": {"default": {"plural": "secondi", "default": "secondo"}}, "category": "time", "names": ["″"]}, {"key": "′", "mappings": {"default": {"plural": "minuti", "default": "minuto"}}, "category": "time", "names": ["min", "′"]}, {"category": "time", "mappings": {"default": {"default": "grado"}}, "key": "°", "names": ["°"]}, {"key": "hr", "mappings": {"default": {"plural": "ore", "default": "ora"}}, "category": "time", "names": ["h", "hr"]}], "it/units/volume.min": [{"locale": "it"}, {"key": "cu", "category": "volume", "names": ["cu", "cu."], "mappings": {"default": {"plural": "cubici", "default": "cubico"}}}, {"key": "cu in", "mappings": {"default": {"plural": "pollici cubici", "default": "pollice cubico"}}, "category": "volume", "names": ["cu in", "cu. in."]}, {"key": "cu ft", "mappings": {"default": {"plural": "piedi cubici", "default": "piede cubico"}}, "category": "volume", "names": ["cu ft", "cu. ft."]}, {"key": "cu yd", "mappings": {"default": {"plural": "iarde cubiche", "default": "iarda cubica"}}, "category": "volume", "names": ["cu yd", "cu. yd."]}, {"key": "bbl", "mappings": {"default": {"plural": "barili", "default": "barile"}}, "category": "volume", "names": ["bbl.", "bbl"]}, {"key": "fl oz", "mappings": {"default": {"plural": "once liquide", "default": "oncia liquida"}}, "category": "volume", "names": ["fl. oz.", "fl oz"]}, {"key": "gal", "mappings": {"default": {"plural": "galloni", "default": "gallone"}}, "category": "volume", "names": ["gal", "gal."]}, {"key": "pt", "mappings": {"default": {"plural": "pinte", "default": "pinta"}}, "category": "volume", "names": ["pt", "pt."]}, {"key": "qt", "mappings": {"default": {"plural": "quarti", "default": "quarto"}}, "category": "volume", "names": ["qt", "qt."]}, {"key": "fl dr", "mappings": {"default": {"plural": "dramme liquide", "default": "dramma liquida"}}, "category": "volume", "names": ["fl dr", "fl. dr."]}, {"key": "tbsp", "mappings": {"default": {"plural": "cucchiai da tavola", "default": "cucchiaio da tavola"}}, "category": "volume", "names": ["tbsp", "tbsp.", "Tbsp", "Tbsp."]}, {"key": "tsp", "mappings": {"default": {"plural": "cucchiai da te", "default": "cucchiaio da te"}}, "category": "volume", "names": ["tsp", "tsp."]}, {"key": "cp", "mappings": {"default": {"plural": "tazze", "default": "tazza"}}, "category": "volume", "names": ["cp", "cp."]}, {"key": "cc", "category": "volume", "names": ["cc", "ccm"], "mappings": {"default": {"plural": "centimetri cubi", "default": "centimetro cubo"}}}, {"key": "l", "mappings": {"default": {"plural": "litri", "default": "litro"}}, "category": "volume", "names": ["l"], "si": true}], "it/units/weight.min": [{"locale": "it"}, {"key": "dr", "mappings": {"default": {"plural": "dramme", "default": "dramma"}}, "category": "weight", "names": ["dr", "dr."]}, {"key": "oz", "mappings": {"default": {"plural": "once", "default": "oncia"}}, "category": "weight", "names": ["oz", "oz."]}, {"key": "lb", "mappings": {"default": {"plural": "libbre", "default": "libbra"}}, "category": "weight", "names": ["lb", "lb."]}, {"key": "st", "category": "weight", "names": ["st", "st."], "mappings": {"default": {"plural": "stone", "default": "stone"}}}, {"key": "qtr", "category": "weight", "names": ["qtr", "qtr."], "mappings": {"default": {"plural": "quarti", "default": "quarto"}}}, {"key": "cwt", "mappings": {"default": {"plural": "hundredweight", "default": "hundredweight"}}, "category": "weight", "names": ["cwt", "cwt."]}, {"key": "LT", "category": "weight", "names": ["LT", "L.T."], "mappings": {"default": {"plural": "long ton", "default": "long ton"}}}, {"key": "gr", "mappings": {"default": {"plural": "grani", "default": "grano"}}, "category": "weight", "names": ["gr"]}, {"key": "g", "mappings": {"default": {"plural": "grammi", "default": "grammo"}}, "category": "weight", "names": ["g"], "si": true}, {"key": "mcg", "category": "weight", "names": ["mcg"], "mappings": {"default": {"plural": "microgrammi", "default": "microgrammo"}}}, {"key": "t", "mappings": {"default": {"plural": "tonnellate", "default": "tonnellata"}}, "category": "weight", "names": ["t", "T"]}], "it/rules/clearspeak_italian.min": {"locale": "it", "domain": "clearspeak", "modality": "speech", "inherits": "romance", "rules": [["Precondition", "function-prefix-det", "default", "self::appl", "@role=\"prefix function\"", "children/*[1][text()=\"det\"]"], ["Precondition", "fraction-per", "Fraction_Per", "self::fraction", "contains(children/*[1]/@annotation, \"clearspeak:unit\")", "contains(children/*[2]/@annotation, \"clearspeak:unit\")"], ["Precondition", "squared-masculine", "default", "self::number[text()=2]", "contains(@grammar, \"squared\")"], ["Precondition", "squared-feminine", "default", "self::number[text()=2]", "contains(@grammar, \"squared\")", "parent::*/parent::*[@role=\"simple function\"]"]]}, "it/rules/clearspeak_italian_actions.min": {"locale": "it", "domain": "clearspeak", "modality": "speech", "kind": "actions", "rules": [["Action", "collapsed", "[n] . (engine:modality=summary, grammar:collapsed)"], ["Action", "font", "[n] . (grammar:ignoreFont=@font); [t] @font (pause:short, grammar:localFont)"], ["Action", "ellipsis", "[t] \"e così via\""], ["Action", "ellipsis-andsoon", "[t] \"e così via fino a\""], ["Action", "vbar-evaluated", "[n] children/*[1] (pause:short); [t] \"valutato a\"; [n] content/*[1]/children/*[2] (pause:short)"], ["Action", "vbar-evaluated-both", "[n] children/*[1] (pause:short); [t] \"valutato a\"; [n] content/*[1]/children/*[2] (pause:short); [t] \"meno la stessa espressione valutata a\"; [n] content/*[1]/children/*[1]/children/*[2] (pause:short)"], ["Action", "vbar-such-that", "[t] \"tale che\""], ["Action", "vbar-divides", "[t] \"divide\""], ["Action", "vbar-always-divides", "[t] \"divide\""], ["Action", "vbar-given", "[t] \"dato\""], ["Action", "member", "[t] \"appartiene ad\""], ["Action", "member-member", "[t] \"membro di\""], ["Action", "member-element", "[t] \"elemento di\""], ["Action", "member-in", "[t] \"in\""], ["Action", "member-belongs", "[t] \"appartiene ad\""], ["Action", "not-member", "[t] \"non appartiene ad\""], ["Action", "not-member-member", "[t] \"non membro di\""], ["Action", "not-member-element", "[t] \"non elemento di\""], ["Action", "not-member-in", "[t] \"non in\""], ["Action", "not-member-belongs", "[t] \"non appartiene ad\""], ["Action", "set-member", "[t] \"in\""], ["Action", "set-member-member", "[t] \"membro di\""], ["Action", "set-member-element", "[t] \"elemento di\""], ["Action", "set-member-belongs", "[t] \"appartenente a\""], ["Action", "set-not-member", "[t] \"non in\""], ["Action", "set-not-member-member", "[t] \"non membro di\""], ["Action", "set-not-member-element", "[t] \"non elemento di\""], ["Action", "set-not-member-belongs", "[t] \"non appartenente a\""], ["Action", "appl", "[n] children/*[1]; [t] \"di\"; [n] children/*[2] (pause:short)"], ["Action", "appl-simple", "[n] children/*[1]; [t] \"di\" (pause:short); [n] children/*[2] (pause:short)"], ["Action", "appl-simple-fenced", "[n] children/*[1]; [t] \"di\" (pause:short); [n] children/*[2] (pause:short)"], ["Action", "appl-times", "[p] (pause:short); [n] children/*[1]; [t] \"per\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix", "[n] children/*[1]; [t] \"di\"; [n] children/*[2]"], ["Action", "function-prefix-det", "[n] children/*[1]; [t] \"della\"; [n] children/*[2]"], ["Action", "function-prefix-simple-arg", "[n] children/*[1]; [t] \"di\"; [n] children/*[2]"], ["Action", "function-prefix-embell", "[p] (pause:short); [n] children/*[1]; [t] \"di\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-fenced-or-frac-arg", "[p] (pause:short); [n] children/*[1]; [t] \"di\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-subscript", "[p] (pause:short); [n] children/*[1]; [t] \"di\" (pause:short); [n] children/*[2] (pause:short)"], ["Action", "function-ln", "[n] children/*[1]; [t] \"di\"; [n] children/*[2]"], ["Action", "function-ln-pause", "[n] children/*[1]; [t] \"di\"; [n] children/*[2] (pause:short)"], ["Action", "function-ln-of", "[n] children/*[1]; [t] \"di\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-as-exp", "[n] children/*[1]; [t] \"di\" (pause:short); [n] children/*[2] (pause:short)"], ["Action", "function-prefix-subscript-as-exp", "[n] children/*[1]; [t] \"di\" (pause:short); [n] children/*[2] (pause:short)"], ["Action", "function-prefix-hyper", "[p] (pause:short); [n] children/*[1]; [t] \"di\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-inverse", "[p] (pause:short); [t] \"inverso del\"; [n] children/*[1]/children/*[1]; [t] \"di\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-reciprocal", "[p] (pause:short); [t] \"il reciproco del\"; [n] children/*[1]/children/*[1] (pause:short); [n] children/*[2] (pause:short)"], ["Action", "function-prefix-reciprocal-simple", "[p] (pause:short); [t] \"il reciproco del\"; [n] children/*[1]/children/*[1]; [n] children/*[2] (pause:short)"], ["Action", "appl-triginverse", "[p] (pause:short); [n] children/*[1]; [t] \"di\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-arc-simple", "[p] (pause:short); [t] \"arco\"; [n] children/*[1]/children/*[1]; [t] \"di\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-arc-simple-fenced", "[p] (pause:short); [t] \"arco\"; [n] children/*[1]/children/*[1]; [t] \"di\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-arc", "[p] (pause:short); [t] \"arco\"; [n] children/*[1]/children/*[1]; [t] \"di\"; [n] children/*[2] (pause:short)"], ["Action", "function-inverse", "[n] children/*[1]; [t] \"inversa\""], ["Action", "superscript-prefix-function", "[t] \"potenza\"; [n] children/*[2] (grammar:ordinal); [t] \"di\"; [n] children/*[1]"], ["Action", "function-reciprocal", "[t] \"riciproco di\"; [n] children/*[1]"], ["Action", "superscript", "[n] children/*[1]; [t] \"elevato all'esponente\" (pause:short); [n] children/*[2] (pause:short); [t] \"fine esponente\" (pause:short)"], ["Action", "superscript-simple-exponent", "[n] children/*[1]; [t] \"elevato alla\"; [n] children/*[2] (pause:medium)"], ["Action", "superscript-simple-exponent-end", "[n] children/*[1]; [t] \"elevato alla\"; [n] children/*[2] (pause:medium)"], ["Action", "superscript-ordinal", "[n] children/*[1]; [t] \"alla\"; [n] children/*[2] (grammar:ordinal); [t] \"potenza\" (pause:short)"], ["Action", "superscript-non-ordinal", "[n] children/*[1]; [t] \"alla\"; [n] children/*[2] (pause:short)"], ["Action", "superscript-simple-function", "[n] . (grammar:functions_none)"], ["Action", "superscript-ordinal-number", "[n] children/*[1]; [t] \"alla\"; [n] children/*[2] (pause:short, grammar:ordinal)"], ["Action", "superscript-ordinal-negative", "[n] children/*[1]; [t] \"alla\"; [n] children/*[2] (pause:short)"], ["Action", "superscript-ordinal-default", "[n] children/*[1]; [t] \"elevato all'esponente\" (pause:short); [n] children/*[2] (pause:short); [t] \"fine esponente\" (pause:short)"], ["Action", "superscript-ordinal-power-number", "[n] children/*[1]; [t] \"alla\"; [n] children/*[2] (grammar:ordinal); [t] \"potenza\" (pause:short)"], ["Action", "superscript-ordinal-power-negative", "[n] children/*[1]; [t] \"alla\"; [n] children/*[2] (pause:short)"], ["Action", "superscript-ordinal-power-identifier", "[n] children/*[1]; [t] \"alla\"; [n] children/*[2] (grammar:ordinal); [t] \"potenza\" (pause:short)"], ["Action", "superscript-ordinal-power-default", "[n] children/*[1]; [t] \"elevato all'esponente\" (pause:short); [n] children/*[2] (pause:short); [t] \"fine esponente\" (pause:short)"], ["Action", "superscript-power", "[n] children/*[1]; [t] \"elevato all'esponente\"; [n] children/*[2] (pause:short, grammar:afterPower)"], ["Action", "superscript-power-default", "[n] children/*[1]; [t] \"elevato all'esponente\" (pause:short); [n] children/*[2] (pause:short); [t] \"fine esponente\" (pause:short)"], ["Action", "exponent", "[n] text() (join:\"-\"); [t] \"esima\""], ["Action", "exponent-number", "[t] CSFordinalExponent"], ["Action", "exponent-ordinal", "[t] CSFwordOrdinal"], ["Action", "exponent-ordinal-zero", "[t] \"zero\""], ["Action", "exponent-ordinalpower", "[t] CSFwordOrdinal"], ["Action", "exponent-ordinalpower-zero", "[t] \"zero\""], ["Action", "squared-masculine", "[t] \"quadrato\""], ["Action", "squared-feminine", "[t] \"quadrata\""], ["Action", "square", "[n] children/*[1]; [n] children/*[2] (grammar:squared)"], ["Action", "cube", "[n] children/*[1]; [t] \"cubo\""], ["Action", "fences-points", "[t] \"punto con coordinate\"; [n] children/*[1]"], ["Action", "fences-interval", "[t] \"l'intervallo da\"; [n] children/*[1]/children/*[1]; [t] \"a\"; [n] children/*[1]/children/*[3] (pause:short); [n] . (grammar:interval)"], ["Action", "interval-open", "[t] \"che non include\"; [n] children/*[1]/children/*[1]; [t] \"o\"; [n] children/*[1]/children/*[3]"], ["Action", "interval-closed-open", "[t] \"che include\"; [n] children/*[1]/children/*[1] (pause:short); [t] \"ma che non include\"; [n] children/*[1]/children/*[3]"], ["Action", "interval-open-closed", "[t] \"che non include\"; [n] children/*[1]/children/*[1] (pause:short); [t] \"ma che comprende\"; [n] children/*[1]/children/*[3]"], ["Action", "interval-closed", "[t] \"che include\"; [n] children/*[1]/children/*[1]; [t] \"e\"; [n] children/*[1]/children/*[3]"], ["Action", "interval-open-inf-r", "[t] \"che non include\"; [n] children/*[1]/children/*[1]"], ["Action", "interval-open-inf-l", "[t] \"che non include\"; [n] children/*[1]/children/*[3]"], ["Action", "interval-closed-open-inf", "[t] \"che include\"; [n] children/*[1]/children/*[1]"], ["Action", "interval-open-closed-inf", "[t] \"che include\"; [n] children/*[1]/children/*[3]"], ["Action", "set-empty", "[t] \"l'insieme vuoto\""], ["Action", "set-extended", "[t] \"l'insieme di tutti\"; [n] children/*[1]/children/*[1]; [t] \"tale che\"; [n] children/*[1]/children/*[3]"], ["Action", "set-collection", "[t] \"l'insieme\"; [n] children/*[1]"], ["Action", "set-extended-woall", "[t] \"l'insieme di\"; [n] children/*[1]/children/*[1]; [t] \"tale che\"; [n] children/*[1]/children/*[3]"], ["Action", "subscript", "[p] (pause:short); [n] children/*[1]; [t] \"pedice\"; [n] children/*[2] (pause:short)"], ["Action", "logarithm-base", "[n] children/*[1]; [t] \"base\"; [n] children/*[2]"], ["Action", "subscript-index", "[n] children/*[1]; [t] \"pedice\"; [n] children/*[2]"], ["Action", "fraction", "[p] (pause:short); [t] \"frazione con numeratore\"; [n] children/*[1] (pause:short); [t] \"e denominatore\"; [n] children/*[2] (pause:short)"], ["Action", "fraction-none", "[p] (pause:short); [t] \"frazione con numeratore\"; [n] children/*[1] (pause:short); [t] \"e denominatore\"; [n] children/*[2] (pause:short)"], ["Action", "simple-fraction", "[p] (pause:short); [n] children/*[1]; [t] \"fratto\"; [n] children/*[2] (pause:short)"], ["Action", "simple-vulgar-fraction", "[p] (pause:short); [n] children/*[1]; [t] \"fratto\"; [n] children/*[2] (pause:short)"], ["Action", "simple-text-fraction", "[p] (pause:short); [n] children/*[1]; [t] \"fratto\"; [n] children/*[2] (pause:short)"], ["Action", "vulgar-fraction", "[t] CSFvulgarFraction (grammar:fraction:gender=\"m\")"], ["Action", "fraction-over", "[p] (pause:short); [n] children/*[1]; [t] \"fratto\"; [n] children/*[2] (pause:short)"], ["Action", "fraction-overendfrac", "[p] (pause:short); [n] children/*[1]; [t] \"fratto\"; [n] children/*[2] (pause:short); [t] \"fine frazione\" (pause:short)"], ["Action", "fraction-fracover", "[p] (pause:short); [t] \"frazione\"; [n] children/*[1]; [t] \"fratto\"; [n] children/*[2] (pause:short)"], ["Action", "fraction-per", "[p] (pause:short); [n] children/*[1]; [t] \"per\"; [n] children/*[2] (pause:short)"], ["Action", "fraction-general<PERSON><PERSON><PERSON>", "[p] (pause:short); [t] \"frazione con numeratore\"; [n] children/*[1] (pause:short); [t] \"e denominatore\"; [n] children/*[2] (pause:short); [t] \"fine frazione\" (pause:short)"], ["Action", "fraction-general", "[p] (pause:short); [t] \"frazione con numeratore\"; [n] children/*[1] (pause:short); [t] \"e denominatore\"; [n] children/*[2] (pause:short)"], ["Action", "simple-vulgar-fraction-ordinal", "[t] CSFvulgarFraction (grammar:fraction:gender=\"m\")"], ["Action", "fraction-endfrac", "[p] (pause:short); [n] . (grammar:endfrac); [t] \"fine frazione\" (pause:short)"], ["Action", "vulgar-fraction-endfrac", "[p] (pause:short); [n] children/*[1]; [t] \"fratto\"; [n] children/*[2] (pause:short)"], ["Action", "simple-vulgar-fraction-endfrac", "[t] CSFvulgarFraction (grammar:fraction:gender=\"m\")"], ["Action", "sqrt", "[t] \"radice quadrata di\"; [n] children/*[1] (pause:short, grammar:!EndRoot)"], ["Action", "sqrt-nested", "[p] (pause:short); [t] \"radice quadrata di\"; [n] children/*[1] (pause:short, grammar:!EndRoot)"], ["Action", "negative-sqrt", "[t] \"meno radice quadrata di\"; [n] children/*[1]/children/*[1] (pause:short, grammar:!EndRoot)"], ["Action", "negative-sqrt-default", "[p] (pause:short); [t] \"meno radice quadrata di\"; [n] children/*[1]/children/*[1] (pause:short, grammar:!EndRoot)"], ["Action", "sqrt-plus-minus", "[t] \"più radice quadrata di\"; [n] children/*[1] (pause:short, grammar:!EndRoot)"], ["Action", "sqrt-nested-plus-minus", "[p] (pause:short); [t] \"più radice quadrata di\"; [n] children/*[1] (pause:short, grammar:!EndRoot)"], ["Action", "sqrt-plus-minus-posnegsqrootend", "[t] \"più radice quadrata di\"; [n] children/*[1] (pause:short, grammar:!EndRoot)"], ["Action", "sqrt-nested-plus-minus-posnegsqrootend", "[p] (pause:short); [t] \"più radice quadrata di\"; [n] children/*[1] (pause:short, grammar:!EndRoot)"], ["Action", "sqrt-endroot", "[n] . (grammar:EndRoot); [t] \"fine radice\" (pause:short)"], ["Action", "negative-sqrt-endroot", "[n] . (grammar:EndRoot); [t] \"fine radice\" (pause:short)"], ["Action", "sqrt-posnegsqrootend", "[n] . (grammar:EndRoot); [t] \"fine radice\" (pause:short)"], ["Action", "negative-sqrt-posnegsqrootend", "[n] . (grammar:EndRoot); [t] \"fine radice\" (pause:short)"], ["Action", "cubic", "[t] \"radice cubica di\"; [n] children/*[2] (pause:short, grammar:!EndRoot)"], ["Action", "cubic-nested", "[p] (pause:short); [t] \"radice cubica di\"; [n] children/*[2] (pause:short, grammar:!EndRoot)"], ["Action", "root", "[t] \"radice\"; [n] children/*[1] (grammar:ordinal:gender=\"f\"); [t] \"di\"; [n] children/*[2] (pause:short, grammar:!EndRoot)"], ["Action", "root-nested", "[p] (pause:short); [t] \"radice\"; [n] children/*[1] (grammar:ordinal:gender=\"f\"); [t] \"di\"; [n] children/*[2] (pause:short, grammar:!EndRoot)"], ["Action", "root-endroot", "[n] . (grammar:EndRoot); [t] \"fine radice\" (pause:short)"], ["Action", "root-posnegsqrootend", "[n] . (grammar:EndRoot); [t] \"fine radice\" (pause:short)"], ["Action", "negative", "[t] \"meno\"; [n] children/*[1]"], ["Action", "positive", "[t] \"più\"; [n] children/*[1]"], ["Action", "angle-measure", "[t] \"misura di\"; [n] content/*[1]; [n] children/*[2] (grammar:angle)"], ["Action", "set-prefix-operators", "[t] \"il\"; [n] . (grammar:!prefix); [t] \"di\""], ["Action", "division", "[n] children/*[1]; [t] \"diviso per\"; [n] children/*[2]"], ["Action", "operators-after-power", "[m] children/* (rate:\"0.5\")"], ["Action", "natural-numbers", "[t] \"i numeri naturali\""], ["Action", "integers", "[t] \"gli interi\""], ["Action", "rational-numbers", "[t] \"i numeri razionali\""], ["Action", "real-numbers", "[t] \"i numeri reali\""], ["Action", "complex-numbers", "[t] \"i numeri complessi\""], ["Action", "natural-numbers-with-zero", "[t] \"i numeri naturali con lo zero\""], ["Action", "positive-integers", "[t] \"gli interi positivi\""], ["Action", "negative-integers", "[t] \"gli interi negativi\""], ["Action", "positive-rational-numbers", "[t] \"i numeri razionali positivi\""], ["Action", "negative-rational-numbers", "[t] \"i numeri razionali negativi\""], ["Action", "fences-neutral", "[p] (pause:short); [t] \"valore assoluto di\"; [n] children/*[1] (pause:short)"], ["Action", "fences-neutral-absend", "[p] (pause:short); [t] \"valore assoluto di\"; [n] children/*[1] (pause:short); [t] \"fine valore assoluto\" (pause:short)"], ["Action", "fences-neutral-cardinality", "[p] (pause:short); [t] \"cardinalità di\"; [n] children/*[1] (pause:short)"], ["Action", "fences-neutral-determinant", "[p] (pause:short); [t] \"determinante della\"; [n] children/*[1] (pause:short)"], ["Action", "fences-metric", "[p] (pause:short); [t] \"metrica di\" (span:.); [n] children/*[1] (pause:short)"], ["Action", "fences-metric-absend", "[p] (pause:short); [t] \"metrica di\" (span:content/*[1]); [n] children/*[1] (pause:short); [t] \"fine metrica\" (span:content/*[1], pause:short)"], ["Action", "matrix", "[t] \"matrice\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Riga-:\", pause:long)"], ["Action", "matrix-simple", "[t] \"matrice\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Riga-:\", pause:long, grammar:simpleDet)"], ["Action", "matrix-trivial", "[t] \"matrice 1 per 1 con valore\"; [n] children/*[1] (pause:long)"], ["Action", "determinant", "[t] \"determinante della matrice\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Riga-:\", pause:long, grammar:simpleDet)"], ["Action", "determinant-simple", "[t] \"determinante della matrice\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Riga-:\", pause:long)"], ["Action", "matrix-vector", "[t] \"matrice colonna\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Riga-:\", pause:long, grammar:simpleDet)"], ["Action", "matrix-vector-simple", "[t] \"matrice colonna\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "matrix-vector-simple-silentcolnum", "[t] \"matrice colonna\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "matrix-row-vector", "[t] \"matrice riga\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/*[1]/children/* (ctxtFunc:CTFnodeCounter, context:\"Colonna-:\", pause:long, grammar:simpleDet)"], ["Action", "matrix-row-vector-simple", "[t] \"matrice riga\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/*[1]/children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "matrix-row-vector-simple-silentcolnum", "[t] \"matrice riga\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/*[1]/children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "matrix-row", "[m] children/* (ctxtFunc:CTFnodeCounter, context:\"Colonna-,- \", sepFunc:CTFpauseSeparator, separator:\"medium\", pause:long)"], ["Action", "matrix-end-matrix", "[n] . (grammar:EndMatrix); [t] \"fine matrice\""], ["Action", "matrix-end-vector", "[n] . (grammar:EndMatrix); [t] \"fine matrice\""], ["Action", "matrix-end-determinant", "[n] . (grammar:EndMatrix); [t] \"fine determinante\""], ["Action", "vector", "[t] \"vettore colonna\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Riga-:\", pause:long, grammar:simpleDet)"], ["Action", "vector-simple", "[t] \"vettore colonna\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "row-vector", "[t] \"vettore riga\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/*[1]/children/* (ctxtFunc:CTFnodeCounter, context:\"Colonna-:\", pause:long, grammar:simpleDet)"], ["Action", "row-vector-simple", "[t] \"vettore riga\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/*[1]/children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "vector-end-matrix", "[n] . (grammar:EndMatrix); [t] \"fine matrice\""], ["Action", "vector-end-vector", "[n] . (grammar:EndMatrix); [t] \"fine vettore\""], ["Action", "vector-end-vector-endvector", "[n] . (grammar:EndMatrix); [t] \"fine vettore\""], ["Action", "vector-end-determinant", "[n] . (grammar:EndMatrix); [t] \"fine determinante\""], ["Action", "binomial", "[t] \"coefficiente binomiale\"; [n] children/*[1]/children/*[1]; [t] \"su\"; [n] children/*[2]/children/*[1]"], ["Action", "lines-summary", "[p] (pause:short); [t] count(children/*); [t] \"linee\"; [n] . (grammar:layoutSummary)"], ["Action", "cases-summary", "[p] (pause:short); [t] count(children/*); [t] \"casi\"; [n] . (grammar:layoutSummary)"], ["Action", "lines", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Riga-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "blank-cell", "[t] \"vuoto\""], ["Action", "blank-line", "[t] \"vuota\""], ["Action", "blank-cell-empty", "[t] \"vuota\""], ["Action", "blank-line-empty", "[t] \"vuota\""], ["Action", "cases", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Caso-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-cases-summary", "[p] (pause:short); [t] count(children/*); [t] \"caso\"; [n] . (grammar:layoutSummary)"], ["Action", "lines-cases", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Caso-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-equations-summary", "[p] (pause:short); [t] count(children/*); [t] \"equazioni\"; [n] . (grammar:layoutSummary)"], ["Action", "lines-equations", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Equazioni-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-steps-summary", "[p] (pause:short); [t] count(children/*); [t] \"passi\"; [n] . (grammar:layoutSummary)"], ["Action", "lines-steps", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Passo-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-rows-summary", "[p] (pause:short); [t] count(children/*); [t] \"riga\"; [n] . (grammar:layoutSummary)"], ["Action", "lines-rows", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Riga-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-constraints-summary", "[p] (pause:short); [t] count(children/*); [t] \"vincoli\"; [n] . (grammar:layoutSummary)"], ["Action", "lines-constraints", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Vincoli-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "bigop", "[t] \"il\"; [n] children/*[1]; [t] \"di\"; [n] children/*[2] (pause:short)"], ["Action", "limboth", "[n] children/*[1]; [t] \"da\"; [n] children/*[2]; [t] \"a\"; [n] children/*[3]"], ["Action", "limlower", "[n] children/*[1]; [t] \"sopra\"; [n] children/*[2] (pause:short)"], ["Action", "limupper", "[n] children/*[1]; [t] \"sotto\"; [n] children/*[2] (pause:short)"], ["Action", "integral", "[t] \"il\"; [n] children/*[1]; [t] \"di\"; [n] children/*[2] (pause:short); [n] children/*[3] (pause:short)"], ["Action", "integral-novar", "[t] \"il\"; [n] children/*[1]; [t] \"di\"; [n] children/*[2] (pause:short)"], ["Action", "overscript", "[n] children/*[1]; [t] \"sotto\"; [n] children/*[2] (pause:short)"], ["Action", "overscript-limits", "[n] children/*[1]; [t] \"a\"; [n] children/*[2]"], ["Action", "underscript", "[n] children/*[1]; [t] \"sopra\"; [n] children/*[2] (pause:short)"], ["Action", "underscript-limits", "[n] children/*[1]; [t] \"da\"; [n] children/*[2]"], ["Action", "mixed-number", "[n] children/*[1]; [t] \"e\"; [n] children/*[2]"], ["Action", "number-with-chars", "[t] \"numero\"; [m] CQFspaceoutNumber (grammar:protected)"], ["Action", "decimal-period", "[t] \"decimale periodico\"; [n] children/*[1] (grammar:spaceout); [t] \"virgola\"; [n] children/*[3]/children/*[1] (grammar:spaceout); [t] \"periodico\""], ["Action", "decimal-period-float", "[t] \"decimale periodico\"; [n] children/*[1] (grammar:spaceout); [t] \"seguito da\"; [n] children/*[2]/children/*[1] (grammar:spaceout); [t] \"periodico\""], ["Action", "decimal-point", "[t] \"punto\""], ["Action", "line-segment", "[t] \"linea segmento\"; [n] children/*[1]/children/*[1]; [n] children/*[1]/children/*[2] (pause:short)"], ["Action", "conjugate", "[t] \"coniugato complesso di\"; [n] children/*[1]"], ["Action", "defined-by", "[t] \"è definito essere\" (pause:short)"], ["Action", "adorned-sign", "[t] \"segno\"; [n] children/*[1]; [t] \"con\"; [n] children/*[2]; [t] \"sopra esso\""], ["Action", "factorial", "[t] \"fattoriale\""], ["Action", "left-super", "[t] \"sub sinistro\"; [n] text()"], ["Action", "left-super-list", "[t] \"sub sinistro\"; [m] children/*"], ["Action", "left-sub", "[t] \"sub sotto\"; [n] text()"], ["Action", "left-sub-list", "[t] \"sub sotto\"; [m] children/*"], ["Action", "right-super", "[t] \"sub sopra\"; [n] text()"], ["Action", "right-super-list", "[t] \"sub sopra\"; [m] children/*"], ["Action", "right-sub", "[t] \"sub destro\"; [n] text()"], ["Action", "right-sub-list", "[t] \"sub destro\"; [m] children/*"], ["Action", "choose", "[t] \"combinazione di\"; [n] children/*[2] (grammar:combinatorics); [t] \"per\"; [n] children/*[4] (grammar:combinatorics)"], ["Action", "permute", "[t] \"permutazione di\"; [n] children/*[2] (grammar:combinatorics); [t] \"per\"; [n] children/*[4] (grammar:combinatorics)"], ["Action", "unit-square", "[n] children/*[1]; [t] \"quadrato\""], ["Action", "unit-cubic", "[n] children/*[1]; [t] \"cubico\""], ["Action", "unit-divide", "[n] children/*[1]; [t] \"al\"; [n] children/*[2] (grammar:singular)"]]}, "it/rules/mathspeak_italian.min": {"domain": "mathspeak", "locale": "it", "modality": "speech", "inherits": "romance", "rules": [["Ignore", "vulgar-fraction"], ["Precondition", "squared-masculine", "default", "self::number[text()=2]", "contains(@grammar, \"squared\")"], ["Precondition", "squared-feminine", "default", "self::number[text()=2]", "contains(@grammar, \"squared\")", "parent::*/parent::*[@role=\"simple function\"]"]]}, "it/rules/mathspeak_italian_actions.min": {"domain": "mathspeak", "locale": "it", "modality": "speech", "kind": "actions", "rules": [["Action", "collapsed", "[n] . (engine:modality=summary, grammar:collapsed)"], ["Action", "blank-cell-empty", "[t] \"vuoto\""], ["Action", "blank-line-empty", "[t] \"vuoto\""], ["Action", "font", "[n] . (grammar:ignoreFont=@font); [t] @font (grammar:localFont)"], ["Action", "mixed-number", "[n] children/*[1]; [t] \"e\"; [n] children/*[2]"], ["Action", "number-with-chars", "[t] \"numero\"; [m] CQFspaceoutNumber (grammar:protected)"], ["Action", "number-with-chars-brief", "[t] \"num\"; [m] CQFspaceoutNumber (grammar:protected)"], ["Action", "number-as-upper-word", "[t] \"parolaMaiuscolo\"; [t] CSFspaceoutText"], ["Action", "number-baseline", "[t] \"linea di base\"; [n] . (grammar:baseline)"], ["Action", "number-baseline-brief", "[t] \"base\"; [n] . (grammar:baseline)"], ["Action", "number-baseline-font", "[t] \"linea di base\"; [n] . (grammar:ignoreFont=@font); [t] @font (grammar:localFont)"], ["Action", "number-baseline-font-brief", "[t] \"base\"; [n] . (grammar:ignoreFont=@font); [t] @font (grammar:localFont)"], ["Action", "negative-number", "[t] \"meno\"; [n] children/*[1]"], ["Action", "negative", "[t] \"meno\"; [n] children/*[1]"], ["Action", "division", "[n] children/*[1]; [t] \"diviso per\"; [n] children/*[2]"], ["Action", "subtraction", "[m] children/* (separator:\"meno\")"], ["Action", "fences-neutral", "[t] \"inizio valore assoluto\"; [n] children/*[1]; [t] \"fine valore assoluto\""], ["Action", "fences-neutral-sbrief", "[t] \"valore assoluto\"; [n] children/*[1]; [t] \"fine valore assoluto\""], ["Action", "fences-metric", "[t] \"inizio metrica\"; [n] children/*[1]; [t] \"fine metrica\""], ["Action", "fences-metric-sbrief", "[t] \"metrica\"; [n] children/*[1]; [t] \"fine metrica\""], ["Action", "empty-set", "[t] \"insieme vuoto\""], ["Action", "fences-set", "[t] \"inizio insieme\"; [n] children/*[1]; [t] \"fine insieme\""], ["Action", "fences-set-sbrief", "[t] \"insieme\"; [n] children/*[1]; [t] \"fine insieme\""], ["Action", "factorial", "[t] \"fattoriale\""], ["Action", "minus", "[t] \"meno\""], ["Action", "vulgar-fraction", "[t] CSFvulgarFraction (grammar:fraction:gender=\"m\")"], ["Action", "continued-fraction-outer", "[t] \"continuo frazione\"; [n] children/*[1]; [t] \"fratto\"; [n] children/*[2]"], ["Action", "continued-fraction-outer-brief", "[t] \"continuo frazione\"; [n] children/*[1]; [t] \"fratto\"; [n] children/*[2]"], ["Action", "continued-fraction-inner", "[t] \"inizio frazione\"; [n] children/*[1]; [t] \"fratto\"; [n] children/*[2]"], ["Action", "continued-fraction-inner-brief", "[t] \"inizio frazione\"; [n] children/*[1]; [t] \"fratto\"; [n] children/*[2]"], ["Action", "continued-fraction-inner-sbrief", "[t] \"frazione\"; [n] children/*[1]; [t] \"fratto\"; [n] children/*[2]"], ["Action", "integral", "[n] children/*[1]; [t] \"pedice\"; [n] children/*[2]; [t] \"apice\"; [n] children/*[3]; [t] \"linea di base\""], ["Action", "integral-brief", "[n] children/*[1]; [t] \"sub\"; [n] children/*[2]; [t] \"sup\"; [n] children/*[3]; [t] \"base\""], ["Action", "logarithm-base", "[n] children/*[1]; [t] \"base\"; [n] children/*[2]"], ["Action", "squared-masculine", "[t] \"quadrato\""], ["Action", "squared-feminine", "[t] \"quadrata\""], ["Action", "square", "[n] children/*[1]; [n] children/*[2] (grammar:squared)"], ["Action", "cube", "[n] children/*[1]; [t] \"cubo\""], ["Action", "prime", "[n] children/*[1]; [n] children/*[2]"], ["Action", "counted-prime", "[t] count(children/*) (grammar:numbers2alpha); [t] \"volte primo\""], ["Action", "counted-prime-multichar", "[t] string-length(text()) (grammar:numbers2alpha); [t] \"volte primo\""], ["Action", "overscore", "[t] \"modificante sopra\"; [n] children/*[1]; [t] \"con\"; [n] children/*[2]"], ["Action", "overscore-brief", "[t] \"mod sopra\"; [n] children/*[1]; [t] \"con\"; [n] children/*[2]"], ["Action", "double-overscore", "[t] \"modificante sopra sopra\"; [n] children/*[1]; [t] \"con\"; [n] children/*[2]"], ["Action", "double-overscore-brief", "[t] \"mod sopra sopra\"; [n] children/*[1]; [t] \"con\"; [n] children/*[2]"], ["Action", "underscore", "[t] \"modificanteSotto\"; [n] children/*[1]; [t] \"con\"; [n] children/*[2]"], ["Action", "underscore-brief", "[t] \"modSotto\"; [n] children/*[1]; [t] \"con\"; [n] children/*[2]"], ["Action", "double-underscore", "[t] \"modificante<PERSON>otto sotto\"; [n] children/*[1]; [t] \"con\"; [n] children/*[2]"], ["Action", "double-underscore-brief", "[t] \"modSotto sotto\"; [n] children/*[1]; [t] \"con\"; [n] children/*[2]"], ["Action", "overbar", "[n] children/*[1]; [t] \"con barra sopra\""], ["Action", "underbar", "[n] children/*[1]; [t] \"con barra sotto\""], ["Action", "overtilde", "[n] children/*[1]; [t] \"con tilde sopra\""], ["Action", "undertilde", "[n] children/*[1]; [t] \"con tilde sotto\""], ["Action", "matrix", "[t] \"inizio\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*); [t] \"matrice\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"riga \"); [t] \"fine matrice\""], ["Action", "matrix-sbrief", "[t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*); [t] \"matrice\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"riga \"); [t] \"fine matrice\""], ["Action", "matrix-row", "[m] children/* (ctxtFunc:CTFordinal<PERSON>ounter, context:\"colonna\", pause:200)"], ["Action", "row-with-label", "[t] \"con etichetta\"; [n] content/*[1]; [t] \"fine etichetta\" (pause:200); [m] children/* (ctxtFunc:CTFordinal<PERSON>ounter, context:\"colonna\")"], ["Action", "row-with-label-brief", "[t] \"etichetta\"; [n] content/*[1]; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"colonna\")"], ["Action", "row-with-text-label", "[t] \"etichetta\"; [t] CSFRemoveParens; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"colonna\")"], ["Action", "empty-row", "[t] \"vuoto\""], ["Action", "empty-cell", "[t] \"vuoto\" (pause:300)"], ["Action", "determinant", "[t] \"inizio\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*); [t] \"determinante\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"riga \"); [t] \"fine determinante\""], ["Action", "determinant-sbrief", "[t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*); [t] \"determinante\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"riga \"); [t] \"fine determinante\""], ["Action", "determinant-simple", "[t] \"inizio\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*); [t] \"determinante\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"riga\", grammar:simpleDet); [t] \"fine determinante\""], ["Action", "determinant-simple-sbrief", "[t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*); [t] \"determinante\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"riga\", grammar:simpleDet); [t] \"fine determinante\""], ["Action", "layout", "[t] \"inizio layout\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"row \"); [t] \"fine layout\""], ["Action", "layout-sbrief", "[t] \"layout\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"row \"); [t] \"fine layout\""], ["Action", "binomial", "[t] \"inizio binomiale o matrice\"; [n] children/*[1]/children/*[1]; [t] \"scelta\"; [n] children/*[2]/children/*[1]; [t] \"fine binomiale o matrice\""], ["Action", "binomial-sbrief", "[t] \"bInomiale o matrice\"; [n] children/*[1]/children/*[1]; [t] \"scelta\"; [n] children/*[2]/children/*[1]; [t] \"fine binomiale o matrice\""], ["Action", "cases", "[t] \"inizio layout\"; [t] \"allargato\"; [n] content/*[1]; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"row \"); [t] \"fine layout\""], ["Action", "cases-sbrief", "[t] \"layout\"; [t] \"allargato\"; [n] content/*[1]; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"row \"); [t] \"fine layout\""], ["Action", "line-with-label", "[t] \"con etichetta\"; [n] content/*[1]; [t] \"fine etichetta\" (pause:200); [m] children/*"], ["Action", "line-with-label-brief", "[t] \"etichetta\"; [n] content/*[1] (pause:200); [m] children/*"], ["Action", "line-with-text-label", "[t] \"etichetta\"; [t] CSFRemoveParens; [m] children/*"], ["Action", "empty-line", "[t] \"vuoto\""], ["Action", "empty-line-with-label", "[t] \"con etichetta\"; [n] content/*[1]; [t] \"fine etichetta\" (pause:200); [t] \"vuoto\""], ["Action", "empty-line-with-label-brief", "[t] \"etichetta\"; [n] content/*[1] (pause:200); [t] \"vuoto\""], ["Action", "enclose", "[t] \"inizio chiuso\"; [t] @role (grammar:localEnclose); [n] children/*[1]; [t] \"fine chiuso\""], ["Action", "leftbar", "[t] \"barra vericale\"; [n] children/*[1]"], ["Action", "rightbar", "[n] children/*[1]; [t] \"barra vericale\""], ["Action", "crossout", "[t] \"cancellato\"; [n] children/*[1]; [t] \"fine cancellato\""], ["Action", "cancel", "[t] \"cancellato\"; [n] children/*[1]/children/*[1]; [t] \"con\"; [n] children/*[2]; [t] \"fine cancellato\""], ["Action", "cancel-reverse", "[t] \"cancellato\"; [n] children/*[2]/children/*[1]; [t] \"con\"; [n] children/*[1]; [t] \"fine cancellato\""], ["Action", "multi-inference", "[t] \"regola di inferenza\"; [m] content/*; [t] \"con conclusione\"; [n] children/*[1]; [t] \"e\"; [t] count(children/*[2]/children/*); [t] \"premesse\""], ["Action", "inference", "[t] \"regola di inferenza\"; [m] content/*; [t] \"con conclusione\"; [n] children/*[1]; [t] \"e\"; [t] count(children/*[2]/children/*); [t] \"premessa\""], ["Action", "premise", "[m] children/* (ctxtFunc:CTFordinalCounter, context:\"premise \")"], ["Action", "conclusion", "[n] children/*[1]"], ["Action", "label", "[t] \"etichetta\"; [n] children/*[1]"], ["Action", "axiom", "[t] \"assioma\"; [m] children/*[1]"], ["Action", "empty-axiom", "[t] \"assioma vuoto\""]]}, "it/rules/prefix_italian.min": {"modality": "prefix", "domain": "default", "locale": "it", "inherits": "base", "rules": []}, "it/rules/prefix_italian_actions.min": {"modality": "prefix", "domain": "default", "locale": "it", "kind": "actions", "rules": [["Action", "numerator", "[t] \"numeratore\" (pause:200)"], ["Action", "denominator", "[t] \"denominatore\" (pause:200)"], ["Action", "base", "[t] \"base\" (pause:200)"], ["Action", "exponent", "[t] \"esponente\" (pause:200)"], ["Action", "subscript", "[t] \"pedice\" (pause:200)"], ["Action", "overscript", "[t] \"apice\" (pause:200)"], ["Action", "underscript", "[t] \"sottoscritto\" (pause:200)"], ["Action", "radicand", "[t] \"radicando\" (pause:200)"], ["Action", "index", "[t] \"indice\" (pause:200)"], ["Action", "leftsub", "[t] \"pedice sinistro\" (pause:200)"], ["Action", "leftsub-counted", "[t] CSFordinalPosition (grammar:gender=\"m\"); [t] \"pedice sinistro\" (pause:200)"], ["Action", "leftsuper", "[t] \"apice sinistro\" (pause:200)"], ["Action", "leftsuper-counted", "[t] CSFordinalPosition (grammar:gender=\"m\"); [t] \"apice sinistro\" (pause:200)"], ["Action", "rightsub", "[t] \"pedice destro\" (pause:200)"], ["Action", "rightsub-counted", "[t] CSFordinalPosition (grammar:gender=\"m\"); [t] \"pedice destro\" (pause:200)"], ["Action", "<PERSON><PERSON>r", "[t] \"apice destro\" (pause:200)"], ["Action", "rightsuper-counted", "[t] CSFordinalPosition (grammar:gender=\"m\"); [t] \"apice destro\" (pause:200)"], ["Action", "choice", "[t] \"quantità scelta\" (pause:200)"], ["Action", "select", "[t] \"quantità selezione\" (pause:200)"], ["Action", "row", "[t] CSFordinalPosition (grammar:gender=\"f\"); [t] \"riga\" (pause:200)"], ["Action", "cell", "[n] ../..; [t] CSFordinalPosition (grammar:gender=\"f\"); [t] \"colonna\" (pause:200)"], ["Action", "cell-simple", "[t] CSFordinalPosition (grammar:gender=\"f\"); [t] \"colonna\" (pause:200)"]]}, "it/rules/summary_italian.min": {"locale": "it", "modality": "summary", "inherits": "romance", "rules": []}, "it/rules/summary_italian_actions.min": {"locale": "it", "modality": "summary", "kind": "actions", "rules": [["Action", "collapsed-masculine", "[t] \"collassato\""], ["Action", "collapsed-feminine", "[t] \"collassata\""], ["Action", "abstr-identifier-long", "[t] \"identificatore lungo\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-identifier", "[t] \"identificatore\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-number-long", "[t] \"intero lungo\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-number", "[t] \"numero\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-mixed-number-long", "[t] \"numero misto lungo\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-mixed-number", "[t] \"numero misto\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-text", "[t] \"testo\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-function", "[t] \"espressione funzionale\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-function-brief", "[t] \"funzione\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-lim", "[t] \"funzione limite\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-lim-brief", "[t] \"limite\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-fraction", "[t] \"frazione\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-fraction-brief", "[t] \"frazione\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-continued-fraction", "[t] \"frazione continua\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-continued-fraction-brief", "[t] \"frazione continua\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-sqrt", "[t] \"radice quadrata\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-sqrt-nested", "[t] \"radice quadrata doppia\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-root-end", "[t] \"radice di indice\"; [n] children/*[1] (engine:modality=speech); [t] \"indice finale\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-root", "[t] \"radice di indice\"; [n] children/*[1] (engine:modality=speech); [n] . (grammar:gender=\"f\")"], ["Action", "abstr-root-brief", "[t] \"radice \"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-root-nested-end", "[t] \"radicale doppio di indice\"; [n] children/*[1] (engine:modality=speech); [t] \"indice finale\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-root-nested", "[t] \"radicale doppio di indice\"; [n] children/*[1] (engine:modality=speech); [n] . (grammar:gender=\"m\")"], ["Action", "abstr-root-nested-brief", "[t] \"radicale doppio\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-superscript", "[t] \"potenza\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-subscript", "[t] \"pedice\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-subsup", "[t] \"potenza con pedice\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-infixop", "[t] @role (grammar:localRole); [t] \"con\"; [t] count(./children/*); [t] \"elementi\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-infixop-var", "[t] @role (grammar:localRole); [t] \"con numero variabile di elementi\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-infixop-brief", "[t] @role (grammar:localRole); [n] . (grammar:gender=\"m\")"], ["Action", "abstr-addition", "[t] \"somma con\"; [t] count(./children/*); [t] \"addendi\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-addition-brief", "[t] \"somma\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-addition-var", "[t] \"somma con numero variabile di addendi\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-multiplication", "[t] \"prodotto di\"; [t] count(./children/*); [t] \"fattori\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-multiplication-brief", "[t] \"prodotto\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-multiplication-var", "[t] \"prodotto con numero variabile di fattori\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-vector", "[t] count(./children/*); [t] \"vettore dimensionale\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-vector-brief", "[t] \"vettore\""], ["Action", "abstr-vector-var", "[t] \"vettore di dimensione n\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-binomial", "[t] \"binomiale\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-determinant", "[t] count(./children/*); [t] \"derminante dimensionale\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-determinant-brief", "[t] \"determinante\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-determinant-var", "[t] \"determinante di dimensione n\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-squarematrix", "[t] count(./children/*); [t] \"matrice quadrata dimensionale\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-squarematrix-brief", "[t] \"matrice quadrata\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-rowvector", "[t] count(./children/row/children/*); [t] \"vettore riga dimensionale\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-rowvector-brief", "[t] \"vettore riga\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-rowvector-var", "[t] \"vettore riga di dimensione n\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-matrix", "[t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*); [t] \"matrice\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-matrix-brief", "[t] \"matrice\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-matrix-var", "[t] \"matrice dimensionale n pr m\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-cases", "[t] \"comando switch\"; [t] \"con\"; [t] count(children/*); [t] \"casi\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-cases-brief", "[t] \"comando switch\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-cases-var", "[t] \"comando di switch con numero variabile di casi\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-punctuated", "[n] content/*[1]; [t] \"lista separata\"; [t] \"di lunghezza\"; [t] count(children/*) - count(content/*); [n] . (grammar:gender=\"f\")"], ["Action", "abstr-punctuated-brief", "[n] content/*[1]; [t] \"lista separata\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-punctuated-var", "[n] content/*[1]; [t] \"lista separata\"; [t] \"di lunghezza variabile\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-bigop", "[n] content/*[1]; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-integral", "[t] \"integrale\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-relation", "[t] @role (grammar:localRole); [n] . (grammar:gender=\"m\")"], ["Action", "abstr-relation-seq", "[t] @role (grammar:localRole); [t] \"sequenza\"; [t] \"con\"; [t] count(./children/*); [t] \"elementi\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-relation-seq-brief", "[t] @role (grammar:localRole); [t] \"sequenza\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-relation-var", "[t] @role (grammar:localRole); [t] \"sequenza\"; [t] \"con numero variabile di elementi\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-multirel", "[t] \"sequenza di relazione\"; [t] \"con\"; [t] count(./children/*); [t] \"elementi\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-multirel-brief", "[t] \"sequenza di relazione\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-multirel-var", "[t] \"sequenza di relazione con numero variabile di elementi\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-table", "[t] \"tavola con\"; [t] count(children/*); [t] \"righe e\"; [t] count(children/*[1]/children/*); [t] \"colonne\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-line", "[t] \"in\"; [t] @role (grammar:localRole); [n] . (grammar:gender=\"m\")"], ["Action", "abstr-row", "[t] \"in\"; [t] @role (grammar:localRole); [t] count(preceding-sibling::..); [t] \"con\"; [t] count(children/*); [t] \"colonne\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-cell", "[t] \"in\"; [t] @role (grammar:localRole); [n] . (grammar:gender=\"f\")"]]}}