/* 深度思考模块样式 */
.reasoning-wrapper {
    margin: 0 0 12px 0;
    padding: 0;
    border: none;
}

.reasoning-toggle {
    cursor: pointer;
    background: transparent;
    color: var(--cerebr-reasoning-text-color);
    font-size: 0.9em;
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    user-select: none;
    transition: all 0.2s ease;
    width: auto;
    display: inline-block;
}

.reasoning-toggle:hover {
    background: var(--cerebr-toggle-hover-bg);
    color: #333;
}

.reasoning-content {
    cursor: pointer;
    background-color: var(--cerebr-reasoning-bg);
    border-radius: 6px;
    font-size: 0.95em;
    color: var(--cerebr-reasoning-text-color);
    line-height: 1.5;
    overflow: hidden;
    transition: all 0.2s ease;
    padding: 8px 12px;
}

.reasoning-content:hover {
    background-color: var(--cerebr-reasoning-hover-bg);
}

.reasoning-placeholder {
    display: none;
    font-size: 0.9em;
    color: var(--cerebr-reasoning-text-color);
}

.reasoning-text {
    word-break: break-word;
    transition: all 0.2s ease;
}

/* 折叠状态 */
.reasoning-content.collapsed {
    padding: 6px 12px;
}

.reasoning-content.collapsed .reasoning-placeholder {
    display: block;
}

.reasoning-content.collapsed .reasoning-text {
    display: none;
}